using LinCom.Model;
using System;
using System.Collections.Generic;

namespace LinCom.Classe
{
    public class LinkedInConversation_Class
    {
        public long ConversationId { get; set; }
        public string Sujet { get; set; }
        public Nullable<bool> IsGroup { get; set; }
        public Nullable<DateTime> CreatedAt { get; set; }
        public Nullable<DateTime> LastActivity { get; set; }
        public string GroupPhoto { get; set; }
        public string GroupDescription { get; set; }
        public Nullable<long> CreatedBy { get; set; }

        // Propriétés calculées pour l'affichage
        public string ParticipantName { get; set; }
        public string ParticipantPhoto { get; set; }
        public string LastMessage { get; set; }
        public Nullable<DateTime> LastMessageTime { get; set; }
        public int UnreadCount { get; set; }
        public bool HasUnread { get; set; }
        public bool IsOnline { get; set; }
        public bool IsPinned { get; set; }
        public bool IsArchived { get; set; }
        public bool IsTyping { get; set; }
        public string TypingUserName { get; set; }

        // Propriétés pour les participants
        public List<LinkedInParticipant_Class> Participants { get; set; }
        public int ParticipantCount { get; set; }

        // Propriétés pour les statistiques
        public int TotalMessages { get; set; }
        public Nullable<DateTime> FirstMessageDate { get; set; }
        public string MostActiveParticipant { get; set; }

        // Propriétés pour les paramètres
        public bool NotificationsEnabled { get; set; }
        public bool SoundEnabled { get; set; }
        public string Theme { get; set; }

        // Navigation properties
        public virtual ICollection<ParticipantConversation> ParticipantConversations { get; set; }
        public virtual ICollection<Message> Messages { get; set; }

        public LinkedInConversation_Class()
        {
            Participants = new List<LinkedInParticipant_Class>();
            ParticipantConversations = new HashSet<ParticipantConversation>();
            Messages = new HashSet<Message>();
            CreatedAt = DateTime.Now;
            LastActivity = DateTime.Now;
            IsGroup = false;
            NotificationsEnabled = true;
            SoundEnabled = true;
            Theme = "default";
        }
    }
}
