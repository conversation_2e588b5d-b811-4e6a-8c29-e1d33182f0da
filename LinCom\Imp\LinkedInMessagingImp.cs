using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;
using System.Data.Entity;

namespace LinCom.Imp
{
    public class LinkedInMessagingImp : ILinkedInMessaging
    {
        #region Gestion des conversations

        public void ChargerConversationsAvecDetails(ListView listView, long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var conversations = (from c in context.Conversations
                                       join pc in context.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                       where pc.MembreId == userId
                                       select new
                                       {
                                           ConversationId = c.ConversationId,
                                           IsGroup = c.IsGroup ?? false,
                                           LastActivity = c.LastActivity ?? DateTime.Now,
                                           // Obtenir l'autre participant (pour les conversations 1-à-1)
                                           OtherParticipant = context.ParticipantConversations
                                               .Where(p => p.ConversationId == c.ConversationId && p.MembreId != userId)
                                               .Select(p => p.Membre)
                                               .FirstOrDefault(),
                                           // Dernier message
                                           LastMessage = context.Messages
                                               .Where(m => m.ConversationId == c.ConversationId)
                                               .OrderByDescending(m => m.DateEnvoi)
                                               .Select(m => m.Contenu)
                                               .FirstOrDefault(),
                                           LastMessageTime = context.Messages
                                               .Where(m => m.ConversationId == c.ConversationId)
                                               .OrderByDescending(m => m.DateEnvoi)
                                               .Select(m => m.DateEnvoi)
                                               .FirstOrDefault(),
                                           // Messages non lus
                                           UnreadCount = context.MessageStatus
                                               .Where(ms => ms.UserId == userId && ms.IsRead == 0)
                                               .Join(context.Messages, ms => ms.MessageId, m => m.MessageId, (ms, m) => m)
                                               .Where(m => m.ConversationId == c.ConversationId)
                                               .Count()
                                       })
                                       .OrderByDescending(c => c.LastActivity)
                                       .ToList();

                    var conversationViewModels = conversations.Select(c => new
                    {
                        ConversationId = c.ConversationId,
                        IsGroup = c.IsGroup,
                        ParticipantName = c.IsGroup ? "Groupe" : (c.OtherParticipant?.Nom + " " + c.OtherParticipant?.Prenom),
                        ParticipantPhoto = c.IsGroup ? "group-default.png" : (c.OtherParticipant?.PhotoProfil ?? "default-avatar.png"),
                        LastMessage = c.LastMessage ?? "Aucun message",
                        LastMessageTime = c.LastMessageTime ?? DateTime.Now,
                        UnreadCount = c.UnreadCount,
                        HasUnread = c.UnreadCount > 0,
                        IsOnline = c.IsGroup ? false : VerifierStatutEnLigne(c.OtherParticipant?.MembreId ?? 0),
                        IsPinned = false, // À implémenter
                        IsTyping = false  // À implémenter avec SignalR
                    }).ToList();

                    listView.DataSource = conversationViewModels;
                    listView.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement des conversations: {ex.Message}");
                throw;
            }
        }

        public void AfficherDetailsConversation(long conversationId, LinkedInConversation_Class conversationClass)
        {
            try
            {
                using (var context = new Connection())
                {
                    var conversation = context.Conversations.FirstOrDefault(c => c.ConversationId == conversationId);
                    if (conversation != null)
                    {
                        conversationClass.ConversationId = conversation.ConversationId;
                        conversationClass.Sujet = conversation.Sujet;
                        conversationClass.IsGroup = conversation.IsGroup;
                        conversationClass.CreatedAt = conversation.CreatedAt;
                        conversationClass.LastActivity = conversation.LastActivity;
                        conversationClass.CreatedBy = conversation.CreatedBy;

                        // Charger les participants
                        var participants = (from pc in context.ParticipantConversations
                                          join m in context.Membres on pc.MembreId equals m.MembreId
                                          where pc.ConversationId == conversationId
                                          select new LinkedInParticipant_Class
                                          {
                                              UserId = m.MembreId,
                                              Name = m.Nom + " " + m.Prenom,
                                              FirstName = m.Prenom,
                                              LastName = m.Nom,
                                              Email = m.Email,
                                              PhotoUrl = m.PhotoProfil ?? "default-avatar.png",
                                              Title = m.Titre,
                                              IsOnline = VerifierStatutEnLigne(m.MembreId),
                                              LastSeen = ObtenirDernierVu(m.MembreId),
                                              JoinedAt = pc.JoinedAt
                                          }).ToList();

                        conversationClass.Participants = participants;
                        conversationClass.ParticipantCount = participants.Count;

                        // Calculer les statistiques
                        conversationClass.TotalMessages = context.Messages.Count(m => m.ConversationId == conversationId);
                        conversationClass.FirstMessageDate = context.Messages
                            .Where(m => m.ConversationId == conversationId)
                            .OrderBy(m => m.DateEnvoi)
                            .Select(m => m.DateEnvoi)
                            .FirstOrDefault();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'affichage des détails de conversation: {ex.Message}");
                throw;
            }
        }

        public int CreerNouvelleConversation(LinkedInConversation_Class conversation, long[] participantIds)
        {
            try
            {
                using (var context = new Connection())
                {
                    var newConversation = new Conversation
                    {
                        Sujet = conversation.Sujet,
                        IsGroup = conversation.IsGroup,
                        CreatedAt = DateTime.Now,
                        LastActivity = DateTime.Now,
                        CreatedBy = conversation.CreatedBy
                    };

                    context.Conversations.Add(newConversation);
                    context.SaveChanges();

                    // Ajouter les participants
                    foreach (var participantId in participantIds)
                    {
                        var participant = new ParticipantConversation
                        {
                            ConversationId = newConversation.ConversationId,
                            MembreId = participantId,
                            JoinedAt = DateTime.Now
                        };
                        context.ParticipantConversations.Add(participant);
                    }

                    context.SaveChanges();
                    return (int)newConversation.ConversationId;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la création de conversation: {ex.Message}");
                return 0;
            }
        }

        public int ModifierConversation(LinkedInConversation_Class conversation)
        {
            try
            {
                using (var context = new Connection())
                {
                    var existingConversation = context.Conversations.FirstOrDefault(c => c.ConversationId == conversation.ConversationId);
                    if (existingConversation != null)
                    {
                        existingConversation.Sujet = conversation.Sujet;
                        existingConversation.LastActivity = DateTime.Now;
                        
                        context.SaveChanges();
                        return 1;
                    }
                    return 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la modification de conversation: {ex.Message}");
                return 0;
            }
        }

        public int SupprimerConversation(long conversationId, long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    // Supprimer la participation de l'utilisateur
                    var participation = context.ParticipantConversations
                        .FirstOrDefault(pc => pc.ConversationId == conversationId && pc.MembreId == userId);
                    
                    if (participation != null)
                    {
                        participation.LeftAt = DateTime.Now;
                        context.SaveChanges();
                        return 1;
                    }
                    return 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la suppression de conversation: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Gestion des participants

        public void ChargerParticipants(Repeater rpt, long conversationId, long currentUserId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var participants = (from pc in context.ParticipantConversations
                                      join m in context.Membres on pc.MembreId equals m.MembreId
                                      where pc.ConversationId == conversationId && pc.MembreId != currentUserId
                                      select new
                                      {
                                          UserId = m.MembreId,
                                          Name = m.Nom + " " + m.Prenom,
                                          PhotoUrl = m.PhotoProfil ?? "default-avatar.png",
                                          Title = m.Titre,
                                          IsOnline = VerifierStatutEnLigne(m.MembreId),
                                          LastSeen = ObtenirDernierVu(m.MembreId),
                                          JoinedAt = pc.JoinedAt
                                      }).ToList();

                    rpt.DataSource = participants;
                    rpt.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement des participants: {ex.Message}");
                throw;
            }
        }

        public void AfficherDetailsParticipant(long userId, LinkedInParticipant_Class participantClass)
        {
            try
            {
                using (var context = new Connection())
                {
                    var membre = context.Membres.FirstOrDefault(m => m.MembreId == userId);
                    if (membre != null)
                    {
                        participantClass.UserId = membre.MembreId;
                        participantClass.Name = membre.Nom + " " + membre.Prenom;
                        participantClass.FirstName = membre.Prenom;
                        participantClass.LastName = membre.Nom;
                        participantClass.Email = membre.Email;
                        participantClass.PhotoUrl = membre.PhotoProfil ?? "default-avatar.png";
                        participantClass.Title = membre.Titre;
                        participantClass.IsOnline = VerifierStatutEnLigne(membre.MembreId);
                        participantClass.LastSeen = ObtenirDernierVu(membre.MembreId);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'affichage des détails du participant: {ex.Message}");
                throw;
            }
        }

        public int AjouterParticipant(long conversationId, long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var existingParticipant = context.ParticipantConversations
                        .FirstOrDefault(pc => pc.ConversationId == conversationId && pc.MembreId == userId);

                    if (existingParticipant == null)
                    {
                        var participant = new ParticipantConversation
                        {
                            ConversationId = conversationId,
                            MembreId = userId,
                            JoinedAt = DateTime.Now
                        };
                        context.ParticipantConversations.Add(participant);
                        context.SaveChanges();
                        return 1;
                    }
                    return 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'ajout du participant: {ex.Message}");
                return 0;
            }
        }

        public int SupprimerParticipant(long conversationId, long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var participant = context.ParticipantConversations
                        .FirstOrDefault(pc => pc.ConversationId == conversationId && pc.MembreId == userId);

                    if (participant != null)
                    {
                        participant.LeftAt = DateTime.Now;
                        context.SaveChanges();
                        return 1;
                    }
                    return 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la suppression du participant: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Gestion des messages

        public void ChargerMessagesAvecDetails(Repeater rpt, long conversationId, int nombreMessages = 50)
        {
            try
            {
                using (var context = new Connection())
                {
                    var messages = (from m in context.Messages
                                  join sender in context.Membres on m.SenderId equals sender.MembreId
                                  where m.ConversationId == conversationId
                                  orderby m.DateEnvoi descending
                                  select new
                                  {
                                      MessageId = m.MessageId,
                                      Content = m.Contenu,
                                      SentAt = m.DateEnvoi ?? DateTime.Now,
                                      SenderId = m.SenderId ?? 0,
                                      SenderName = sender.Nom + " " + sender.Prenom,
                                      SenderPhoto = sender.PhotoProfil ?? "default-avatar.png",
                                      AttachmentUrl = m.AttachmentUrl,
                                      AttachmentName = m.name,
                                      AttachmentType = GetAttachmentTypeFromUrl(m.AttachmentUrl)
                                  }).Take(nombreMessages).ToList();

                    // Inverser l'ordre pour affichage chronologique
                    var orderedMessages = messages.OrderBy(m => m.SentAt).ToList();

                    rpt.DataSource = orderedMessages;
                    rpt.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement des messages: {ex.Message}");
                throw;
            }
        }

        public void AfficherDetailsMessage(long messageId, LinkedInMessage_Class messageClass)
        {
            try
            {
                using (var context = new Connection())
                {
                    var message = (from m in context.Messages
                                 join sender in context.Membres on m.SenderId equals sender.MembreId
                                 where m.MessageId == messageId
                                 select new
                                 {
                                     Message = m,
                                     Sender = sender
                                 }).FirstOrDefault();

                    if (message != null)
                    {
                        messageClass.MessageId = message.Message.MessageId;
                        messageClass.ConversationId = message.Message.ConversationId;
                        messageClass.SenderId = message.Message.SenderId;
                        messageClass.Contenu = message.Message.Contenu;
                        messageClass.AttachmentUrl = message.Message.AttachmentUrl;
                        messageClass.DateEnvoi = message.Message.DateEnvoi;
                        messageClass.name = message.Message.name;
                        messageClass.SenderName = message.Sender.Nom + " " + message.Sender.Prenom;
                        messageClass.SenderPhoto = message.Sender.PhotoProfil ?? "default-avatar.png";
                        messageClass.AttachmentType = GetAttachmentTypeFromUrl(message.Message.AttachmentUrl);

                        // Charger les statuts de lecture
                        var readStatuses = (from ms in context.MessageStatus
                                          join u in context.Membres on ms.UserId equals u.MembreId
                                          where ms.MessageId == messageId
                                          select new LinkedInMessageStatus_Class
                                          {
                                              MessageStatusId = ms.MessageStatusId,
                                              MessageId = ms.MessageId,
                                              UserId = ms.UserId,
                                              IsRead = ms.IsRead,
                                              ReadAt = ms.ReadAt,
                                              UserName = u.Nom + " " + u.Prenom,
                                              UserPhoto = u.PhotoProfil ?? "default-avatar.png"
                                          }).ToList();

                        messageClass.ReadByUsers = readStatuses;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'affichage des détails du message: {ex.Message}");
                throw;
            }
        }

        public int EnvoyerMessageAvecStatut(LinkedInMessage_Class message)
        {
            try
            {
                using (var context = new Connection())
                {
                    var newMessage = new Message
                    {
                        ConversationId = message.ConversationId,
                        SenderId = message.SenderId,
                        Contenu = message.Contenu,
                        AttachmentUrl = message.AttachmentUrl,
                        DateEnvoi = DateTime.Now,
                        name = message.name
                    };

                    context.Messages.Add(newMessage);
                    context.SaveChanges();

                    // Créer les statuts de lecture pour tous les participants
                    var participants = context.ParticipantConversations
                        .Where(pc => pc.ConversationId == message.ConversationId && pc.MembreId != message.SenderId)
                        .ToList();

                    foreach (var participant in participants)
                    {
                        var status = new MessageStatu
                        {
                            MessageId = newMessage.MessageId,
                            UserId = participant.MembreId,
                            IsRead = 0,
                            ReadAt = null
                        };
                        context.MessageStatus.Add(status);
                    }

                    // Mettre à jour l'activité de la conversation
                    var conversation = context.Conversations.FirstOrDefault(c => c.ConversationId == message.ConversationId);
                    if (conversation != null)
                    {
                        conversation.LastActivity = DateTime.Now;
                    }

                    context.SaveChanges();
                    return (int)newMessage.MessageId;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi du message: {ex.Message}");
                return 0;
            }
        }

        public int ModifierMessage(LinkedInMessage_Class message)
        {
            try
            {
                using (var context = new Connection())
                {
                    var existingMessage = context.Messages.FirstOrDefault(m => m.MessageId == message.MessageId);
                    if (existingMessage != null)
                    {
                        existingMessage.Contenu = message.Contenu;
                        context.SaveChanges();
                        return 1;
                    }
                    return 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la modification du message: {ex.Message}");
                return 0;
            }
        }

        public int SupprimerMessage(long messageId, long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var message = context.Messages.FirstOrDefault(m => m.MessageId == messageId && m.SenderId == userId);
                    if (message != null)
                    {
                        context.Messages.Remove(message);
                        context.SaveChanges();
                        return 1;
                    }
                    return 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la suppression du message: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Recherche et filtres

        public void RechercherConversations(ListView listView, long userId, string searchTerm)
        {
            try
            {
                using (var context = new Connection())
                {
                    var conversations = (from c in context.Conversations
                                       join pc in context.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                       where pc.MembreId == userId
                                       select new
                                       {
                                           ConversationId = c.ConversationId,
                                           IsGroup = c.IsGroup ?? false,
                                           Sujet = c.Sujet,
                                           LastActivity = c.LastActivity ?? DateTime.Now,
                                           OtherParticipant = context.ParticipantConversations
                                               .Where(p => p.ConversationId == c.ConversationId && p.MembreId != userId)
                                               .Select(p => p.Membre)
                                               .FirstOrDefault(),
                                           LastMessage = context.Messages
                                               .Where(m => m.ConversationId == c.ConversationId)
                                               .OrderByDescending(m => m.DateEnvoi)
                                               .Select(m => m.Contenu)
                                               .FirstOrDefault()
                                       })
                                       .Where(c => (c.Sujet != null && c.Sujet.Contains(searchTerm)) ||
                                                  (c.OtherParticipant != null && 
                                                   (c.OtherParticipant.Nom.Contains(searchTerm) || 
                                                    c.OtherParticipant.Prenom.Contains(searchTerm))) ||
                                                  (c.LastMessage != null && c.LastMessage.Contains(searchTerm)))
                                       .OrderByDescending(c => c.LastActivity)
                                       .ToList();

                    var conversationViewModels = conversations.Select(c => new
                    {
                        ConversationId = c.ConversationId,
                        IsGroup = c.IsGroup,
                        ParticipantName = c.IsGroup ? (c.Sujet ?? "Groupe") : (c.OtherParticipant?.Nom + " " + c.OtherParticipant?.Prenom),
                        ParticipantPhoto = c.IsGroup ? "group-default.png" : (c.OtherParticipant?.PhotoProfil ?? "default-avatar.png"),
                        LastMessage = c.LastMessage ?? "Aucun message",
                        LastMessageTime = c.LastActivity,
                        IsOnline = c.IsGroup ? false : VerifierStatutEnLigne(c.OtherParticipant?.MembreId ?? 0)
                    }).ToList();

                    listView.DataSource = conversationViewModels;
                    listView.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la recherche de conversations: {ex.Message}");
                throw;
            }
        }

        public void FiltrerConversations(ListView listView, long userId, string filterType)
        {
            try
            {
                using (var context = new Connection())
                {
                    var query = from c in context.Conversations
                               join pc in context.ParticipantConversations on c.ConversationId equals pc.ConversationId
                               where pc.MembreId == userId
                               select new
                               {
                                   ConversationId = c.ConversationId,
                                   IsGroup = c.IsGroup ?? false,
                                   LastActivity = c.LastActivity ?? DateTime.Now,
                                   OtherParticipant = context.ParticipantConversations
                                       .Where(p => p.ConversationId == c.ConversationId && p.MembreId != userId)
                                       .Select(p => p.Membre)
                                       .FirstOrDefault(),
                                   LastMessage = context.Messages
                                       .Where(m => m.ConversationId == c.ConversationId)
                                       .OrderByDescending(m => m.DateEnvoi)
                                       .Select(m => m.Contenu)
                                       .FirstOrDefault(),
                                   UnreadCount = context.MessageStatus
                                       .Where(ms => ms.UserId == userId && ms.IsRead == 0)
                                       .Join(context.Messages, ms => ms.MessageId, m => m.MessageId, (ms, m) => m)
                                       .Where(m => m.ConversationId == c.ConversationId)
                                       .Count()
                               };

                    switch (filterType?.ToLower())
                    {
                        case "unread":
                            query = query.Where(c => c.UnreadCount > 0);
                            break;
                        case "archived":
                            // À implémenter avec une table de paramètres
                            query = query.Where(c => false); // Temporaire
                            break;
                        case "all":
                        default:
                            // Pas de filtre supplémentaire
                            break;
                    }

                    var conversations = query.OrderByDescending(c => c.LastActivity).ToList();

                    var conversationViewModels = conversations.Select(c => new
                    {
                        ConversationId = c.ConversationId,
                        IsGroup = c.IsGroup,
                        ParticipantName = c.IsGroup ? "Groupe" : (c.OtherParticipant?.Nom + " " + c.OtherParticipant?.Prenom),
                        ParticipantPhoto = c.IsGroup ? "group-default.png" : (c.OtherParticipant?.PhotoProfil ?? "default-avatar.png"),
                        LastMessage = c.LastMessage ?? "Aucun message",
                        LastMessageTime = c.LastActivity,
                        UnreadCount = c.UnreadCount,
                        HasUnread = c.UnreadCount > 0,
                        IsOnline = c.IsGroup ? false : VerifierStatutEnLigne(c.OtherParticipant?.MembreId ?? 0)
                    }).ToList();

                    listView.DataSource = conversationViewModels;
                    listView.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du filtrage des conversations: {ex.Message}");
                throw;
            }
        }

        public void RechercherMessages(Repeater rpt, long userId, string searchTerm, int nombreResultats = 20)
        {
            try
            {
                using (var context = new Connection())
                {
                    var messages = (from m in context.Messages
                                  join sender in context.Membres on m.SenderId equals sender.MembreId
                                  join pc in context.ParticipantConversations on m.ConversationId equals pc.ConversationId
                                  where pc.MembreId == userId && m.Contenu.Contains(searchTerm)
                                  orderby m.DateEnvoi descending
                                  select new
                                  {
                                      MessageId = m.MessageId,
                                      ConversationId = m.ConversationId,
                                      Content = m.Contenu,
                                      SentAt = m.DateEnvoi ?? DateTime.Now,
                                      SenderName = sender.Nom + " " + sender.Prenom,
                                      SenderPhoto = sender.PhotoProfil ?? "default-avatar.png",
                                      SearchHighlight = m.Contenu // À améliorer avec highlighting
                                  }).Take(nombreResultats).ToList();

                    rpt.DataSource = messages;
                    rpt.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la recherche de messages: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Statuts et présence

        public int MettreAJourStatutPresence(long userId, bool isOnline)
        {
            try
            {
                // À implémenter avec une table UserPresence ou cache Redis
                System.Diagnostics.Debug.WriteLine($"Statut de présence mis à jour pour l'utilisateur {userId}: {(isOnline ? "en ligne" : "hors ligne")}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la mise à jour du statut de présence: {ex.Message}");
                return 0;
            }
        }

        public int MettreAJourStatutFrappe(long conversationId, long userId, bool isTyping)
        {
            try
            {
                // À implémenter avec SignalR ou une table TypingIndicators
                System.Diagnostics.Debug.WriteLine($"Statut de frappe mis à jour pour l'utilisateur {userId} dans la conversation {conversationId}: {isTyping}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la mise à jour du statut de frappe: {ex.Message}");
                return 0;
            }
        }

        public bool VerifierStatutEnLigne(long userId)
        {
            try
            {
                // À implémenter avec un système de présence en temps réel
                // Pour l'instant, simuler avec une logique basée sur la dernière activité
                var lastSeen = ObtenirDernierVu(userId);
                return lastSeen.HasValue && (DateTime.Now - lastSeen.Value).TotalMinutes < 5;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la vérification du statut en ligne: {ex.Message}");
                return false;
            }
        }

        public DateTime? ObtenirDernierVu(long userId)
        {
            try
            {
                // À implémenter avec une table UserPresence
                // Pour l'instant, retourner null
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'obtention du dernier vu: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Fonctionnalités avancées

        public int EpinglerConversation(long conversationId, long userId, bool isPinned)
        {
            try
            {
                // À implémenter avec une table ConversationSettings
                System.Diagnostics.Debug.WriteLine($"Conversation {conversationId} {(isPinned ? "épinglée" : "désépinglée")} pour l'utilisateur {userId}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'épinglage de conversation: {ex.Message}");
                return 0;
            }
        }

        public int ArchiverConversation(long conversationId, long userId, bool isArchived)
        {
            try
            {
                // À implémenter avec une table ConversationSettings
                System.Diagnostics.Debug.WriteLine($"Conversation {conversationId} {(isArchived ? "archivée" : "désarchivée")} pour l'utilisateur {userId}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'archivage de conversation: {ex.Message}");
                return 0;
            }
        }

        public int AjouterReactionMessage(long messageId, long userId, string reactionType)
        {
            try
            {
                // À implémenter avec une table MessageReactions
                System.Diagnostics.Debug.WriteLine($"Réaction {reactionType} ajoutée au message {messageId} par l'utilisateur {userId}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'ajout de réaction: {ex.Message}");
                return 0;
            }
        }

        public int SupprimerReactionMessage(long messageId, long userId, string reactionType)
        {
            try
            {
                // À implémenter avec une table MessageReactions
                System.Diagnostics.Debug.WriteLine($"Réaction {reactionType} supprimée du message {messageId} par l'utilisateur {userId}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la suppression de réaction: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Statistiques

        public void ObtenirStatistiquesMessagerie(long userId, LinkedInMessagingStats_Class statsClass)
        {
            try
            {
                using (var context = new Connection())
                {
                    // Nombre total de conversations
                    statsClass.TotalConversations = context.ParticipantConversations
                        .Where(pc => pc.MembreId == userId)
                        .Count();

                    // Messages non lus
                    statsClass.UnreadMessages = (from ms in context.MessageStatus
                                                join m in context.Messages on ms.MessageId equals m.MessageId
                                                join pc in context.ParticipantConversations on m.ConversationId equals pc.ConversationId
                                                where pc.MembreId == userId && ms.UserId == userId && ms.IsRead == 0
                                                select ms).Count();

                    // Messages envoyés aujourd'hui
                    var today = DateTime.Today;
                    statsClass.MessagesSentToday = context.Messages
                        .Where(m => m.SenderId == userId && 
                                   m.DateEnvoi >= today && 
                                   m.DateEnvoi < today.AddDays(1))
                        .Count();

                    // Conversations actives (avec activité dans les 7 derniers jours)
                    var weekAgo = DateTime.Now.AddDays(-7);
                    statsClass.ActiveConversations = (from pc in context.ParticipantConversations
                                                     join c in context.Conversations on pc.ConversationId equals c.ConversationId
                                                     where pc.MembreId == userId && 
                                                           (c.LastActivity ?? DateTime.MinValue) >= weekAgo
                                                     select pc).Count();

                    // Messages totaux
                    statsClass.TotalMessages = (from m in context.Messages
                                               join pc in context.ParticipantConversations on m.ConversationId equals pc.ConversationId
                                               where pc.MembreId == userId
                                               select m).Count();

                    // Première et dernière activité
                    var userMessages = (from m in context.Messages
                                      join pc in context.ParticipantConversations on m.ConversationId equals pc.ConversationId
                                      where pc.MembreId == userId
                                      select m.DateEnvoi).ToList();

                    if (userMessages.Any())
                    {
                        statsClass.FirstMessageDate = userMessages.Min();
                        statsClass.LastMessageDate = userMessages.Max();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'obtention des statistiques: {ex.Message}");
                throw;
            }
        }

        public int CompterMessagesNonLus(long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    return (from ms in context.MessageStatus
                           join m in context.Messages on ms.MessageId equals m.MessageId
                           join pc in context.ParticipantConversations on m.ConversationId equals pc.ConversationId
                           where pc.MembreId == userId && ms.UserId == userId && ms.IsRead == 0
                           select ms).Count();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du comptage des messages non lus: {ex.Message}");
                return 0;
            }
        }

        public int CompterConversationsActives(long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var weekAgo = DateTime.Now.AddDays(-7);
                    return (from pc in context.ParticipantConversations
                           join c in context.Conversations on pc.ConversationId equals c.ConversationId
                           where pc.MembreId == userId && 
                                 (c.LastActivity ?? DateTime.MinValue) >= weekAgo
                           select pc).Count();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du comptage des conversations actives: {ex.Message}");
                return 0;
            }
        }

        public int CompterMessagesEnvoyesAujourdhui(long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var today = DateTime.Today;
                    return context.Messages
                        .Where(m => m.SenderId == userId && 
                                   m.DateEnvoi >= today && 
                                   m.DateEnvoi < today.AddDays(1))
                        .Count();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du comptage des messages envoyés aujourd'hui: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Notifications

        public int EnvoyerNotificationNouveauMessage(long senderId, long receiverId, long messageId)
        {
            try
            {
                // À implémenter avec votre système de notifications existant
                System.Diagnostics.Debug.WriteLine($"Notification de nouveau message envoyée de {senderId} à {receiverId} pour le message {messageId}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi de notification: {ex.Message}");
                return 0;
            }
        }

        public int EnvoyerNotificationMessageLu(long messageId, long readerId)
        {
            try
            {
                // À implémenter avec votre système de notifications existant
                System.Diagnostics.Debug.WriteLine($"Notification de message lu envoyée pour le message {messageId} par {readerId}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi de notification de lecture: {ex.Message}");
                return 0;
            }
        }

        public int MarquerNotificationsCommeLues(long userId)
        {
            try
            {
                // À implémenter avec votre système de notifications existant
                System.Diagnostics.Debug.WriteLine($"Notifications marquées comme lues pour l'utilisateur {userId}");
                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du marquage des notifications: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Gestion des fichiers

        public string UploadFichierMessage(HttpPostedFile file, long userId)
        {
            try
            {
                if (file != null && file.ContentLength > 0)
                {
                    // Générer un nom de fichier unique
                    var fileName = Guid.NewGuid().ToString() + "_" + file.FileName;
                    var uploadPath = HttpContext.Current.Server.MapPath("~/file/messages/");
                    
                    // Créer le dossier s'il n'existe pas
                    if (!System.IO.Directory.Exists(uploadPath))
                    {
                        System.IO.Directory.CreateDirectory(uploadPath);
                    }

                    var filePath = System.IO.Path.Combine(uploadPath, fileName);
                    file.SaveAs(filePath);

                    return "~/file/messages/" + fileName;
                }
                return "";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'upload de fichier: {ex.Message}");
                return "";
            }
        }

        public bool ValiderFichierMessage(HttpPostedFile file)
        {
            try
            {
                if (file == null || file.ContentLength == 0)
                    return false;

                // Vérifier la taille (max 10 MB)
                if (file.ContentLength > 10 * 1024 * 1024)
                    return false;

                // Vérifier les types autorisés
                var allowedTypes = new[] { 
                    "image/jpeg", "image/png", "image/gif", "image/webp",
                    "application/pdf", "text/plain",
                    "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "audio/mpeg", "audio/wav", "audio/ogg",
                    "video/mp4", "video/avi", "video/mov"
                };

                return allowedTypes.Contains(file.ContentType.ToLower());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la validation de fichier: {ex.Message}");
                return false;
            }
        }

        public string ObtenirTypeIconeFichier(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "fas fa-file";

            var extension = System.IO.Path.GetExtension(fileName).ToLower();

            switch (extension)
            {
                case ".jpg":
                case ".jpeg":
                case ".png":
                case ".gif":
                case ".webp":
                    return "fas fa-image";
                case ".pdf":
                    return "fas fa-file-pdf";
                case ".doc":
                case ".docx":
                    return "fas fa-file-word";
                case ".mp3":
                case ".wav":
                case ".ogg":
                    return "fas fa-file-audio";
                case ".mp4":
                case ".avi":
                case ".mov":
                    return "fas fa-file-video";
                case ".txt":
                    return "fas fa-file-alt";
                default:
                    return "fas fa-file";
            }
        }

        public string ObtenirTailleFichier(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                    return "0 B";

                var fullPath = HttpContext.Current.Server.MapPath(filePath);
                if (System.IO.File.Exists(fullPath))
                {
                    var fileInfo = new System.IO.FileInfo(fullPath);
                    var size = fileInfo.Length;

                    string[] sizes = { "B", "KB", "MB", "GB" };
                    double len = size;
                    int order = 0;

                    while (len >= 1024 && order < sizes.Length - 1)
                    {
                        order++;
                        len = len / 1024;
                    }

                    return $"{len:0.##} {sizes[order]}";
                }
                return "0 B";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'obtention de la taille de fichier: {ex.Message}");
                return "0 B";
            }
        }

        #endregion

        #region Utilitaires

        public string FormatTempsEcoule(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "À l'instant";
            else if (timeSpan.TotalMinutes < 60)
                return $"Il y a {(int)timeSpan.TotalMinutes} min";
            else if (timeSpan.TotalHours < 24)
                return $"Il y a {(int)timeSpan.TotalHours}h";
            else if (timeSpan.TotalDays < 7)
                return $"Il y a {(int)timeSpan.TotalDays}j";
            else
                return dateTime.ToString("dd/MM/yyyy");
        }

        public string TronquerMessage(string message, int longueurMax = 50)
        {
            if (string.IsNullOrEmpty(message))
                return "";

            return message.Length > longueurMax ? message.Substring(0, longueurMax) + "..." : message;
        }

        public string ConvertirEmojisEnTexte(string message)
        {
            if (string.IsNullOrEmpty(message))
                return "";

            // Convertir les émojis en codes texte
            var emojiMap = new Dictionary<string, string>
            {
                { "😊", ":smile:" },
                { "👍", ":thumbs_up:" },
                { "❤️", ":heart:" },
                { "😂", ":laugh:" },
                { "🔥", ":fire:" },
                { "🎉", ":party:" }
            };

            var result = message;
            foreach (var emoji in emojiMap)
            {
                result = result.Replace(emoji.Key, emoji.Value);
            }

            return result;
        }

        public string ConvertirTexteEnEmojis(string message)
        {
            if (string.IsNullOrEmpty(message))
                return "";

            // Convertir les codes texte en émojis
            var emojiMap = new Dictionary<string, string>
            {
                { ":smile:", "😊" },
                { ":thumbs_up:", "👍" },
                { ":heart:", "❤️" },
                { ":laugh:", "😂" },
                { ":fire:", "🔥" },
                { ":party:", "🎉" }
            };

            var result = message;
            foreach (var emoji in emojiMap)
            {
                result = result.Replace(emoji.Key, emoji.Value);
            }

            return result;
        }

        #endregion

        #region Méthodes privées

        private string GetAttachmentTypeFromUrl(string attachmentUrl)
        {
            if (string.IsNullOrEmpty(attachmentUrl))
                return "";

            var extension = System.IO.Path.GetExtension(attachmentUrl).ToLower();

            switch (extension)
            {
                case ".jpg":
                case ".jpeg":
                case ".png":
                case ".gif":
                case ".webp":
                    return "image";
                case ".pdf":
                    return "pdf";
                case ".doc":
                case ".docx":
                    return "document";
                case ".mp3":
                case ".wav":
                case ".ogg":
                    return "audio";
                case ".mp4":
                case ".avi":
                case ".mov":
                    return "video";
                default:
                    return "file";
            }
        }

        #endregion
    }
}
