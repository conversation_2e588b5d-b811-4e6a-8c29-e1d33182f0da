using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Text;

namespace LinCom
{
    public partial class messagerie_linkedin : System.Web.UI.Page
    {
        // Services
        private readonly IMessage _messageService;
        private readonly IConversation _conversationService;
        private readonly IMembre _membreService;
        private readonly MessagerieService _messagerieService;

        // Classes métier
        private Message_Class messageClass = new Message_Class();
        private Conversation_Class conversationClass = new Conversation_Class();
        private Membre_Class membreClass = new Membre_Class();

        // Variables de session
        private long CurrentUserId => Session["MembreId"] != null ? Convert.ToInt64(Session["MembreId"]) : 0;
        private long CurrentConversationId => hdnConversationId.Value != "" ? Convert.ToInt64(hdnConversationId.Value) : 0;

        public messagerie_linkedin()
        {
            _messageService = new MessageImp();
            _conversationService = new ConversationImp();
            _membreService = new MembreImp();
            _messagerieService = new MessagerieService();
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                InitializePage();
                LoadConversations();
            }
            else
            {
                HandlePostBack();
            }
        }

        private void InitializePage()
        {
            // Vérifier l'authentification
            if (CurrentUserId == 0)
            {
                Response.Redirect("login.aspx");
                return;
            }

            // Initialiser les champs cachés
            hdnCurrentUserId.Value = CurrentUserId.ToString();

            // Charger les données initiales
            LoadUserProfile();
        }

        private void LoadUserProfile()
        {
            try
            {
                using (var context = new Connection())
                {
                    var currentUser = context.Membres.FirstOrDefault(m => m.MembreId == CurrentUserId);
                    if (currentUser != null)
                    {
                        // Mettre à jour le statut en ligne
                        UpdateOnlineStatus(CurrentUserId, true);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement du profil: {ex.Message}");
            }
        }

        private void LoadConversations()
        {
            try
            {
                var conversations = GetUserConversations(CurrentUserId);
                listConversations.DataSource = conversations;
                listConversations.DataBind();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement des conversations: {ex.Message}");
                ShowNotification("Erreur lors du chargement des conversations", "error");
            }
        }

        private List<ConversationViewModel> GetUserConversations(long userId)
        {
            using (var context = new Connection())
            {
                var conversations = (from c in context.Conversations
                                   join pc in context.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                   where pc.MembreId == userId
                                   select new
                                   {
                                       ConversationId = c.ConversationId,
                                       IsGroup = c.IsGroup ?? false,
                                       LastActivity = c.LastActivity ?? DateTime.Now,
                                       // Obtenir l'autre participant (pour les conversations 1-à-1)
                                       OtherParticipant = context.ParticipantConversations
                                           .Where(p => p.ConversationId == c.ConversationId && p.MembreId != userId)
                                           .Select(p => p.Membre)
                                           .FirstOrDefault(),
                                       // Dernier message
                                       LastMessage = context.Messages
                                           .Where(m => m.ConversationId == c.ConversationId)
                                           .OrderByDescending(m => m.DateEnvoi)
                                           .Select(m => m.Contenu)
                                           .FirstOrDefault(),
                                       LastMessageTime = context.Messages
                                           .Where(m => m.ConversationId == c.ConversationId)
                                           .OrderByDescending(m => m.DateEnvoi)
                                           .Select(m => m.DateEnvoi)
                                           .FirstOrDefault(),
                                       // Messages non lus
                                       UnreadCount = context.MessageStatus
                                           .Where(ms => ms.UserId == userId && ms.IsRead == 0)
                                           .Join(context.Messages, ms => ms.MessageId, m => m.MessageId, (ms, m) => m)
                                           .Where(m => m.ConversationId == c.ConversationId)
                                           .Count()
                                   })
                                   .OrderByDescending(c => c.LastActivity)
                                   .ToList();

                return conversations.Select(c => new ConversationViewModel
                {
                    ConversationId = c.ConversationId,
                    IsGroup = c.IsGroup,
                    ParticipantName = c.IsGroup ? "Groupe" : (c.OtherParticipant?.Nom + " " + c.OtherParticipant?.Prenom),
                    ParticipantPhoto = c.IsGroup ? "group-default.png" : (c.OtherParticipant?.PhotoProfil ?? "default-avatar.png"),
                    LastMessage = c.LastMessage ?? "Aucun message",
                    LastMessageTime = c.LastMessageTime ?? DateTime.Now,
                    UnreadCount = c.UnreadCount,
                    HasUnread = c.UnreadCount > 0,
                    IsOnline = c.IsGroup ? false : IsUserOnline(c.OtherParticipant?.MembreId ?? 0),
                    IsPinned = false, // À implémenter
                    IsTyping = false  // À implémenter avec SignalR
                }).ToList();
            }
        }

        protected void listConversations_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "selectConversation")
            {
                long conversationId = Convert.ToInt64(e.CommandArgument);
                SelectConversation(conversationId);
            }
        }

        private void SelectConversation(long conversationId)
        {
            try
            {
                hdnConversationId.Value = conversationId.ToString();
                
                // Charger les messages de la conversation
                LoadMessages(conversationId);
                
                // Marquer les messages comme lus
                MarkMessagesAsRead(conversationId, CurrentUserId);
                
                // Mettre à jour l'en-tête de la conversation
                UpdateConversationHeader(conversationId);
                
                // Actualiser la liste des conversations pour mettre à jour les compteurs
                LoadConversations();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la sélection de conversation: {ex.Message}");
                ShowNotification("Erreur lors de l'ouverture de la conversation", "error");
            }
        }

        private void LoadMessages(long conversationId)
        {
            try
            {
                var messages = GetConversationMessages(conversationId);
                rptMessages.DataSource = messages;
                rptMessages.DataBind();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement des messages: {ex.Message}");
            }
        }

        private List<MessageViewModel> GetConversationMessages(long conversationId)
        {
            using (var context = new Connection())
            {
                var messages = (from m in context.Messages
                              join sender in context.Membres on m.SenderId equals sender.MembreId
                              where m.ConversationId == conversationId
                              orderby m.DateEnvoi
                              select new MessageViewModel
                              {
                                  MessageId = m.MessageId,
                                  Content = m.Contenu,
                                  SentAt = m.DateEnvoi ?? DateTime.Now,
                                  SenderId = m.SenderId ?? 0,
                                  SenderName = sender.Nom + " " + sender.Prenom,
                                  SenderPhoto = sender.PhotoProfil ?? "default-avatar.png",
                                  IsSentByCurrentUser = m.SenderId == CurrentUserId,
                                  AttachmentUrl = m.AttachmentUrl,
                                  AttachmentName = m.name,
                                  AttachmentType = GetAttachmentType(m.AttachmentUrl),
                                  Status = GetMessageStatus(m.MessageId, CurrentUserId)
                              }).ToList();

                return messages;
            }
        }

        private void UpdateConversationHeader(long conversationId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var conversation = context.Conversations.FirstOrDefault(c => c.ConversationId == conversationId);
                    if (conversation != null)
                    {
                        if (conversation.IsGroup == true)
                        {
                            lblCurrentParticipant.InnerText = "Conversation de groupe";
                            imgCurrentParticipant.Src = ResolveUrl("~/assets/img/group-default.png");
                        }
                        else
                        {
                            // Trouver l'autre participant
                            var otherParticipant = (from pc in context.ParticipantConversations
                                                  join m in context.Membres on pc.MembreId equals m.MembreId
                                                  where pc.ConversationId == conversationId && pc.MembreId != CurrentUserId
                                                  select m).FirstOrDefault();

                            if (otherParticipant != null)
                            {
                                lblCurrentParticipant.InnerText = otherParticipant.Nom + " " + otherParticipant.Prenom;
                                imgCurrentParticipant.Src = ResolveUrl("~/file/membr/" + (otherParticipant.PhotoProfil ?? "default-avatar.png"));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la mise à jour de l'en-tête: {ex.Message}");
            }
        }

        protected void btnSendMessage_Click(object sender, EventArgs e)
        {
            try
            {
                string messageContent = txtMessage.Text.Trim();
                
                if (string.IsNullOrEmpty(messageContent) || CurrentConversationId == 0)
                {
                    return;
                }

                // Valider le message
                var validation = MessagerieValidator.ValiderMessage(messageContent);
                if (!validation.EstValide)
                {
                    ShowNotification(validation.Message, "error");
                    return;
                }

                // Traiter les émojis
                messageContent = EmojiManager.ConvertirEmojis(messageContent);

                // Envoyer le message
                long messageId = SendMessage(CurrentConversationId, CurrentUserId, messageContent);

                if (messageId > 0)
                {
                    // Créer les statuts de lecture pour tous les participants
                    CreateMessageStatuses(messageId, CurrentConversationId);

                    // Envoyer les notifications
                    SendMessageNotifications(messageId, CurrentConversationId, CurrentUserId);

                    // Vider le champ de saisie
                    txtMessage.Text = "";

                    // Recharger les messages
                    LoadMessages(CurrentConversationId);

                    // Actualiser les conversations
                    LoadConversations();

                    ShowNotification("Message envoyé", "success");
                }
                else
                {
                    ShowNotification("Erreur lors de l'envoi du message", "error");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi du message: {ex.Message}");
                ShowNotification("Erreur lors de l'envoi du message", "error");
            }
        }

        protected void txtRechercheContact_TextChanged(object sender, EventArgs e)
        {
            string searchTerm = txtRechercheContact.Text.Trim();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                LoadConversations();
            }
            else
            {
                SearchConversations(searchTerm);
            }
        }

        private void SearchConversations(string searchTerm)
        {
            try
            {
                var conversations = GetUserConversations(CurrentUserId)
                    .Where(c => c.ParticipantName.ToLower().Contains(searchTerm.ToLower()) ||
                               c.LastMessage.ToLower().Contains(searchTerm.ToLower()))
                    .ToList();

                listConversations.DataSource = conversations;
                listConversations.DataBind();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void HandlePostBack()
        {
            string eventTarget = Request["__EVENTTARGET"];
            string eventArgument = Request["__EVENTARGUMENT"];

            switch (eventTarget)
            {
                case "refreshMessages":
                    if (CurrentConversationId > 0)
                    {
                        LoadMessages(CurrentConversationId);
                    }
                    break;
            }
        }

        // Méthodes utilitaires pour les templates
        protected string GetAvatarUrl(object photoPath)
        {
            if (photoPath == null || string.IsNullOrEmpty(photoPath.ToString()))
            {
                return ResolveUrl("~/assets/img/default-avatar.png");
            }
            return ResolveUrl("~/file/membr/" + photoPath.ToString());
        }

        protected string GetTimeAgo(object dateTime)
        {
            if (dateTime == null) return "";
            
            DateTime date = Convert.ToDateTime(dateTime);
            TimeSpan timeSpan = DateTime.Now - date;

            if (timeSpan.TotalMinutes < 1)
                return "À l'instant";
            else if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}m";
            else if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}h";
            else if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}j";
            else
                return date.ToString("dd/MM");
        }

        protected string TruncateMessage(object message)
        {
            if (message == null) return "";
            
            string msg = message.ToString();
            return msg.Length > 50 ? msg.Substring(0, 50) + "..." : msg;
        }

        protected string ProcessMessageContent(object content)
        {
            if (content == null) return "";
            
            string processedContent = HttpUtility.HtmlEncode(content.ToString());
            processedContent = EmojiManager.ConvertirEmojis(processedContent);
            
            // Convertir les liens en liens cliquables
            processedContent = ConvertLinksToClickable(processedContent);
            
            return processedContent;
        }

        protected string RenderAttachment(object attachmentUrl, object attachmentName, object attachmentType)
        {
            if (attachmentUrl == null || string.IsNullOrEmpty(attachmentUrl.ToString()))
                return "";

            string url = attachmentUrl.ToString();
            string name = attachmentName?.ToString() ?? "Fichier";
            string type = attachmentType?.ToString() ?? "";

            if (type.StartsWith("image/"))
            {
                return $"<img src='{url}' alt='{name}' class='message-image' onclick='openImageModal(this.src)' />";
            }
            else
            {
                string icon = GetFileIconHtml(type);
                return $@"
                    <div class='attachment-item'>
                        <span class='attachment-icon'>{icon}</span>
                        <div class='attachment-info'>
                            <span class='attachment-name'>{name}</span>
                            <span class='attachment-size'>{GetFileSizeFromUrl(url)}</span>
                        </div>
                        <a href='{url}' target='_blank' class='attachment-download'>
                            <i class='fas fa-download'></i>
                        </a>
                    </div>";
            }
        }

        protected string GetFormattedTime(object dateTime)
        {
            if (dateTime == null) return "";
            
            DateTime date = Convert.ToDateTime(dateTime);
            return date.ToString("HH:mm");
        }

        protected string GetMessageStatusIcon(object status)
        {
            if (status == null) return "";
            
            switch (status.ToString().ToLower())
            {
                case "sent":
                    return "<i class='fas fa-check' title='Envoyé'></i>";
                case "delivered":
                    return "<i class='fas fa-check-double' title='Livré'></i>";
                case "read":
                    return "<i class='fas fa-check-double text-primary' title='Lu'></i>";
                default:
                    return "";
            }
        }

        // Méthodes privées utilitaires
        private long SendMessage(long conversationId, long senderId, string content)
        {
            using (var context = new Connection())
            {
                var message = new Message
                {
                    ConversationId = conversationId,
                    SenderId = senderId,
                    Contenu = content,
                    DateEnvoi = DateTime.Now
                };

                context.Messages.Add(message);
                context.SaveChanges();

                return message.MessageId;
            }
        }

        private void CreateMessageStatuses(long messageId, long conversationId)
        {
            using (var context = new Connection())
            {
                var participants = context.ParticipantConversations
                    .Where(pc => pc.ConversationId == conversationId && pc.MembreId != CurrentUserId)
                    .ToList();

                foreach (var participant in participants)
                {
                    var status = new MessageStatu
                    {
                        MessageId = messageId,
                        UserId = participant.MembreId,
                        IsRead = 0,
                        ReadAt = null
                    };

                    context.MessageStatus.Add(status);
                }

                context.SaveChanges();
            }
        }

        private void SendMessageNotifications(long messageId, long conversationId, long senderId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var participants = context.ParticipantConversations
                        .Where(pc => pc.ConversationId == conversationId && pc.MembreId != senderId)
                        .Select(pc => pc.MembreId)
                        .ToList();

                    var sender = context.Membres.FirstOrDefault(m => m.MembreId == senderId);
                    string senderName = sender?.Nom + " " + sender?.Prenom;

                    foreach (var participantId in participants)
                    {
                        MessageNotificationManager.NotifierNouveauMessage(
                            senderId, participantId, "Nouveau message", conversationId);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi des notifications: {ex.Message}");
            }
        }

        private void MarkMessagesAsRead(long conversationId, long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var unreadStatuses = (from ms in context.MessageStatus
                                        join m in context.Messages on ms.MessageId equals m.MessageId
                                        where m.ConversationId == conversationId && 
                                              ms.UserId == userId && 
                                              ms.IsRead == 0
                                        select ms).ToList();

                    foreach (var status in unreadStatuses)
                    {
                        status.IsRead = 1;
                        status.ReadAt = DateTime.Now;
                    }

                    context.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du marquage des messages comme lus: {ex.Message}");
            }
        }

        private bool IsUserOnline(long userId)
        {
            // À implémenter avec un système de présence en temps réel
            // Pour l'instant, retourner false
            return false;
        }

        private void UpdateOnlineStatus(long userId, bool isOnline)
        {
            // À implémenter avec un système de présence en temps réel
        }

        private string GetAttachmentType(string attachmentUrl)
        {
            if (string.IsNullOrEmpty(attachmentUrl)) return "";
            
            string extension = Path.GetExtension(attachmentUrl).ToLower();
            
            switch (extension)
            {
                case ".jpg":
                case ".jpeg":
                case ".png":
                case ".gif":
                    return "image/" + extension.Substring(1);
                case ".pdf":
                    return "application/pdf";
                case ".doc":
                case ".docx":
                    return "application/msword";
                default:
                    return "application/octet-stream";
            }
        }

        private string GetMessageStatus(long messageId, long currentUserId)
        {
            using (var context = new Connection())
            {
                var message = context.Messages.FirstOrDefault(m => m.MessageId == messageId);
                if (message?.SenderId != currentUserId) return "";

                var statuses = context.MessageStatus.Where(ms => ms.MessageId == messageId).ToList();
                
                if (statuses.All(s => s.IsRead == 1))
                    return "read";
                else if (statuses.Any())
                    return "delivered";
                else
                    return "sent";
            }
        }

        private string ConvertLinksToClickable(string text)
        {
            // Regex pour détecter les URLs
            string pattern = @"(https?://[^\s]+)";
            return System.Text.RegularExpressions.Regex.Replace(text, pattern, 
                "<a href='$1' target='_blank' class='message-link'>$1</a>");
        }

        private string GetFileIconHtml(string mimeType)
        {
            if (mimeType.StartsWith("image/")) return "<i class='fas fa-image'></i>";
            if (mimeType == "application/pdf") return "<i class='fas fa-file-pdf'></i>";
            if (mimeType.Contains("word")) return "<i class='fas fa-file-word'></i>";
            if (mimeType.StartsWith("audio/")) return "<i class='fas fa-file-audio'></i>";
            if (mimeType.StartsWith("video/")) return "<i class='fas fa-file-video'></i>";
            return "<i class='fas fa-file'></i>";
        }

        private string GetFileSizeFromUrl(string url)
        {
            // À implémenter selon votre système de stockage
            return "Fichier";
        }

        private void ShowNotification(string message, string type)
        {
            // Ajouter un script pour afficher la notification côté client
            string script = $"messaging.showNotification('{message}', '{type}');";
            ClientScript.RegisterStartupScript(this.GetType(), "notification", script, true);
        }
    }

    // Classes ViewModel pour les données
    public class ConversationViewModel
    {
        public long ConversationId { get; set; }
        public bool IsGroup { get; set; }
        public string ParticipantName { get; set; }
        public string ParticipantPhoto { get; set; }
        public string LastMessage { get; set; }
        public DateTime LastMessageTime { get; set; }
        public int UnreadCount { get; set; }
        public bool HasUnread { get; set; }
        public bool IsOnline { get; set; }
        public bool IsPinned { get; set; }
        public bool IsTyping { get; set; }
    }

    public class MessageViewModel
    {
        public long MessageId { get; set; }
        public string Content { get; set; }
        public DateTime SentAt { get; set; }
        public long SenderId { get; set; }
        public string SenderName { get; set; }
        public string SenderPhoto { get; set; }
        public bool IsSentByCurrentUser { get; set; }
        public string AttachmentUrl { get; set; }
        public string AttachmentName { get; set; }
        public string AttachmentType { get; set; }
        public string Status { get; set; }
    }
}
