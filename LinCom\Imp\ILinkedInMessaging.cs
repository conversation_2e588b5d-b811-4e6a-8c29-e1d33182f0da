using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ILinkedInMessaging
    {
        // Gestion des conversations
        void ChargerConversationsAvecDetails(ListView listView, long userId);
        void AfficherDetailsConversation(long conversationId, LinkedInConversation_Class conversationClass);
        int CreerNouvelleConversation(LinkedInConversation_Class conversation, long[] participantIds);
        int ModifierConversation(LinkedInConversation_Class conversation);
        int SupprimerConversation(long conversationId, long userId);

        // Gestion des participants
        void ChargerParticipants(Repeater rpt, long conversationId, long currentUserId);
        void AfficherDetailsParticipant(long userId, LinkedInParticipant_Class participantClass);
        int AjouterParticipant(long conversationId, long userId);
        int SupprimerParticipant(long conversationId, long userId);

        // Gestion des messages
        void ChargerMessagesAvecDetails(Repeater rpt, long conversationId, int nombreMessages = 50);
        void AfficherDetailsMessage(long messageId, LinkedInMessage_Class messageClass);
        int EnvoyerMessageAvecStatut(LinkedInMessage_Class message);
        int ModifierMessage(LinkedInMessage_Class message);
        int SupprimerMessage(long messageId, long userId);

        // Recherche et filtres
        void RechercherConversations(ListView listView, long userId, string searchTerm);
        void FiltrerConversations(ListView listView, long userId, string filterType);
        void RechercherMessages(Repeater rpt, long userId, string searchTerm, int nombreResultats = 20);

        // Statuts et présence
        int MettreAJourStatutPresence(long userId, bool isOnline);
        int MettreAJourStatutFrappe(long conversationId, long userId, bool isTyping);
        bool VerifierStatutEnLigne(long userId);
        DateTime? ObtenirDernierVu(long userId);

        // Fonctionnalités avancées
        int EpinglerConversation(long conversationId, long userId, bool isPinned);
        int ArchiverConversation(long conversationId, long userId, bool isArchived);
        int AjouterReactionMessage(long messageId, long userId, string reactionType);
        int SupprimerReactionMessage(long messageId, long userId, string reactionType);

        // Statistiques
        void ObtenirStatistiquesMessagerie(long userId, LinkedInMessagingStats_Class statsClass);
        int CompterMessagesNonLus(long userId);
        int CompterConversationsActives(long userId);
        int CompterMessagesEnvoyesAujourdhui(long userId);

        // Notifications
        int EnvoyerNotificationNouveauMessage(long senderId, long receiverId, long messageId);
        int EnvoyerNotificationMessageLu(long messageId, long readerId);
        int MarquerNotificationsCommeLues(long userId);

        // Gestion des fichiers
        string UploadFichierMessage(System.Web.HttpPostedFile file, long userId);
        bool ValiderFichierMessage(System.Web.HttpPostedFile file);
        string ObtenirTypeIconeFichier(string fileName);
        string ObtenirTailleFichier(string filePath);

        // Utilitaires
        string FormatTempsEcoule(DateTime dateTime);
        string TronquerMessage(string message, int longueurMax = 50);
        string ConvertirEmojisEnTexte(string message);
        string ConvertirTexteEnEmojis(string message);
    }
}
