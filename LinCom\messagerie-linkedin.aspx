<%@ Page Title="Messagerie LinCom - Style LinkedIn" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie-linkedin.aspx.cs" Inherits="LinCom.messagerie_linkedin" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- LinkedIn-style Messaging CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <link href="Content/linkedin-messaging.css" rel="stylesheet" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- LinkedIn-style Messaging Interface -->
    <div class="linkedin-messaging-container">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
        <asp:HiddenField ID="hdnIsGroup" runat="server" />
        <asp:HiddenField ID="hdnCurrentUserId" runat="server" />
        <asp:HiddenField ID="hdnTypingUserId" runat="server" />
        <asp:HiddenField ID="hdnLastMessageId" runat="server" />

        <!-- Main Messaging Layout -->
        <div class="messaging-layout">
            
            <!-- Left Sidebar - Conversations List -->
            <div class="conversations-sidebar">
                <!-- Header with Search -->
                <div class="sidebar-header">
                    <div class="header-title">
                        <i class="fas fa-comments"></i>
                        <span>Messagerie</span>
                    </div>
                    <div class="header-actions">
                        <button type="button" class="action-btn" id="btnNewMessage" title="Nouveau message">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="action-btn" id="btnSettings" title="Paramètres">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <asp:TextBox ID="txtRechercheContact" runat="server" 
                            placeholder="Rechercher dans les messages..." 
                            AutoPostBack="true" 
                            OnTextChanged="txtRechercheContact_TextChanged" 
                            CssClass="search-input-modern"></asp:TextBox>
                        <button type="button" class="search-filter-btn" title="Filtres">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>

                <!-- Filter Tabs -->
                <div class="filter-tabs">
                    <button type="button" class="filter-tab active" data-filter="all">
                        <span>Toutes</span>
                        <span class="tab-count">12</span>
                    </button>
                    <button type="button" class="filter-tab" data-filter="unread">
                        <span>Non lues</span>
                        <span class="tab-count">3</span>
                    </button>
                    <button type="button" class="filter-tab" data-filter="archived">
                        <span>Archivées</span>
                    </button>
                </div>

                <!-- Conversations List -->
                <div class="conversations-list">
                    <asp:ListView ID="listConversations" runat="server" OnItemCommand="listConversations_ItemCommand">
                        <EmptyDataTemplate>
                            <div class="empty-conversations">
                                <i class="fas fa-comments"></i>
                                <p>Aucune conversation</p>
                                <small>Commencez une nouvelle conversation</small>
                            </div>
                        </EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectConversation" runat="server"
                                CommandName="selectConversation"
                                CommandArgument='<%# Eval("ConversationId") %>'
                                CssClass='<%# "conversation-item" + (Convert.ToBoolean(Eval("HasUnread")) ? " has-unread" : "") %>'>
                                
                                <div class="conversation-avatar">
                                    <img src='<%# GetAvatarUrl(Eval("ParticipantPhoto")) %>' 
                                         alt="Photo de profil" />
                                    <div class='<%# "online-status " + (Convert.ToBoolean(Eval("IsOnline")) ? "online" : "offline") %>'></div>
                                </div>
                                
                                <div class="conversation-content">
                                    <div class="conversation-header">
                                        <span class="contact-name"><%# HttpUtility.HtmlEncode(Eval("ParticipantName")) %></span>
                                        <span class="last-message-time"><%# GetTimeAgo(Eval("LastMessageTime")) %></span>
                                    </div>
                                    <div class="conversation-preview">
                                        <span class="last-message"><%# TruncateMessage(Eval("LastMessage")) %></span>
                                        <div class="conversation-badges">
                                            <asp:Panel runat="server" Visible='<%# Convert.ToInt32(Eval("UnreadCount")) > 0 %>'>
                                                <span class="unread-count"><%# Eval("UnreadCount") %></span>
                                            </asp:Panel>
                                            <asp:Panel runat="server" Visible='<%# Convert.ToBoolean(Eval("IsPinned")) %>'>
                                                <i class="fas fa-thumbtack pin-icon"></i>
                                            </asp:Panel>
                                        </div>
                                    </div>
                                    <div class="conversation-indicators">
                                        <asp:Panel runat="server" Visible='<%# Convert.ToBoolean(Eval("IsTyping")) %>'>
                                            <span class="typing-indicator">
                                                <i class="fas fa-circle"></i>
                                                <i class="fas fa-circle"></i>
                                                <i class="fas fa-circle"></i>
                                            </span>
                                        </asp:Panel>
                                    </div>
                                </div>
                            </asp:LinkButton>
                        </ItemTemplate>
                    </asp:ListView>
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="chat-main-area">
                <!-- Chat Header -->
                <div class="chat-header-modern">
                    <div class="chat-participant-info">
                        <div class="participant-avatar">
                            <img id="imgCurrentParticipant" runat="server" src="" alt="Photo de profil" />
                            <div class="online-status online"></div>
                        </div>
                        <div class="participant-details">
                            <h3 id="lblCurrentParticipant" runat="server">Sélectionnez une conversation</h3>
                            <span class="participant-status">En ligne</span>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button type="button" class="chat-action-btn" title="Appel vocal">
                            <i class="fas fa-phone"></i>
                        </button>
                        <button type="button" class="chat-action-btn" title="Appel vidéo">
                            <i class="fas fa-video"></i>
                        </button>
                        <button type="button" class="chat-action-btn" title="Informations">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <div class="dropdown">
                            <button type="button" class="chat-action-btn dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-thumbtack"></i> Épingler</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-archive"></i> Archiver</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-ban"></i> Bloquer</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash"></i> Supprimer</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Messages Area -->
                <div class="messages-container">
                    <div class="messages-scroll-area" id="messagesScrollArea">
                        <!-- Typing Indicator -->
                        <div class="typing-indicator-container" id="typingIndicator" style="display: none;">
                            <div class="message-bubble typing-bubble">
                                <div class="typing-animation">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>

                        <!-- Messages Repeater -->
                        <asp:Repeater ID="rptMessages" runat="server">
                            <ItemTemplate>
                                <div class='<%# "message-group " + (Convert.ToBoolean(Eval("IsSentByCurrentUser")) ? "sent" : "received") %>'>
                                    <div class="message-avatar">
                                        <img src='<%# GetAvatarUrl(Eval("SenderPhoto")) %>' alt="Avatar" />
                                    </div>
                                    <div class="message-content">
                                        <div class="message-bubble">
                                            <div class="message-text">
                                                <%# ProcessMessageContent(Eval("Content")) %>
                                            </div>
                                            
                                            <!-- Attachments -->
                                            <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                                                <div class="message-attachment">
                                                    <%# RenderAttachment(Eval("AttachmentUrl"), Eval("AttachmentName"), Eval("AttachmentType")) %>
                                                </div>
                                            </asp:Panel>
                                            
                                            <!-- Message Reactions -->
                                            <div class="message-reactions">
                                                <button type="button" class="reaction-btn" data-reaction="like">
                                                    <i class="far fa-thumbs-up"></i>
                                                    <span>2</span>
                                                </button>
                                                <button type="button" class="reaction-btn" data-reaction="love">
                                                    <i class="far fa-heart"></i>
                                                    <span>1</span>
                                                </button>
                                                <button type="button" class="add-reaction-btn">
                                                    <i class="far fa-smile"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="message-meta">
                                            <span class="message-time"><%# GetFormattedTime(Eval("SentAt")) %></span>
                                            <asp:Panel runat="server" Visible='<%# Convert.ToBoolean(Eval("IsSentByCurrentUser")) %>'>
                                                <span class="message-status">
                                                    <%# GetMessageStatusIcon(Eval("Status")) %>
                                                </span>
                                            </asp:Panel>
                                        </div>
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                    </div>
                </div>

                <!-- Message Input Area -->
                <div class="message-input-area">
                    <!-- Quick Actions Bar -->
                    <div class="quick-actions-bar" id="quickActionsBar" style="display: none;">
                        <button type="button" class="quick-action-btn" data-action="gif">
                            <i class="fas fa-images"></i>
                            <span>GIF</span>
                        </button>
                        <button type="button" class="quick-action-btn" data-action="sticker">
                            <i class="fas fa-sticky-note"></i>
                            <span>Sticker</span>
                        </button>
                        <button type="button" class="quick-action-btn" data-action="poll">
                            <i class="fas fa-poll"></i>
                            <span>Sondage</span>
                        </button>
                    </div>

                    <!-- Main Input Container -->
                    <div class="input-container">
                        <div class="input-toolbar">
                            <button type="button" class="toolbar-btn" id="btnAttachment" title="Joindre un fichier">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button type="button" class="toolbar-btn" id="btnEmoji" title="Émojis">
                                <i class="far fa-smile"></i>
                            </button>
                            <button type="button" class="toolbar-btn" id="btnGif" title="GIF">
                                <i class="fas fa-images"></i>
                            </button>
                        </div>

                        <div class="message-input-wrapper">
                            <asp:TextBox ID="txtMessage" runat="server" 
                                TextMode="MultiLine" 
                                placeholder="Écrivez un message..." 
                                CssClass="message-input-modern"
                                onkeyup="handleTyping()"
                                onkeydown="handleKeyDown(event)"
                                MaxLength="1000"></asp:TextBox>
                            
                            <!-- Voice Message Button -->
                            <button type="button" class="voice-message-btn" id="btnVoiceMessage" title="Message vocal">
                                <i class="fas fa-microphone"></i>
                            </button>
                        </div>

                        <asp:Button ID="btnSendMessage" runat="server" 
                            OnClick="btnSendMessage_Click" 
                            CssClass="send-btn-modern" 
                            Text="">
                            <i class="fas fa-paper-plane"></i>
                        </asp:Button>
                    </div>

                    <!-- File Upload (Hidden) -->
                    <input type="file" id="fileUpload" style="display: none;" 
                           accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.rar,.mp3,.mp4,.avi"
                           multiple />

                    <!-- Character Counter -->
                    <div class="character-counter">
                        <span id="charCount">0/1000</span>
                    </div>
                </div>
            </div>

            <!-- Right Sidebar - Chat Info (Optional) -->
            <div class="chat-info-sidebar" id="chatInfoSidebar" style="display: none;">
                <div class="info-header">
                    <h4>Informations de la conversation</h4>
                    <button type="button" class="close-info-btn" onclick="toggleChatInfo()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="info-content">
                    <!-- Participant Info -->
                    <div class="info-section">
                        <h5>Participant</h5>
                        <div class="participant-card">
                            <img src="" alt="Photo de profil" />
                            <div>
                                <h6>Nom du participant</h6>
                                <p>Titre professionnel</p>
                            </div>
                        </div>
                    </div>

                    <!-- Shared Files -->
                    <div class="info-section">
                        <h5>Fichiers partagés</h5>
                        <div class="shared-files-list">
                            <!-- Files will be loaded here -->
                        </div>
                    </div>

                    <!-- Conversation Settings -->
                    <div class="info-section">
                        <h5>Paramètres</h5>
                        <div class="settings-list">
                            <div class="setting-item">
                                <span>Notifications</span>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emoji Picker Modal -->
        <div class="emoji-picker-modal" id="emojiPickerModal" style="display: none;">
            <div class="emoji-picker-content">
                <div class="emoji-picker-header">
                    <div class="emoji-categories">
                        <button type="button" class="emoji-category active" data-category="recent">🕒</button>
                        <button type="button" class="emoji-category" data-category="people">😊</button>
                        <button type="button" class="emoji-category" data-category="nature">🌿</button>
                        <button type="button" class="emoji-category" data-category="food">🍎</button>
                        <button type="button" class="emoji-category" data-category="activity">⚽</button>
                        <button type="button" class="emoji-category" data-category="travel">🚗</button>
                        <button type="button" class="emoji-category" data-category="objects">💡</button>
                        <button type="button" class="emoji-category" data-category="symbols">❤️</button>
                    </div>
                    <button type="button" class="close-emoji-btn" onclick="closeEmojiPicker()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="emoji-grid" id="emojiGrid">
                    <!-- Emojis will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="Scripts/linkedin-messaging.js"></script>
    <script>
        // Configuration spécifique à ASP.NET
        document.addEventListener('DOMContentLoaded', function() {
            // Intégration avec les contrôles ASP.NET
            const messageInput = document.getElementById('<%= txtMessage.ClientID %>');
            const sendButton = document.getElementById('<%= btnSendMessage.ClientID %>');

            if (messageInput && sendButton) {
                // Désactiver le bouton d'envoi par défaut pour utiliser notre logique
                sendButton.style.display = 'none';

                // Créer notre propre bouton d'envoi
                const customSendBtn = document.createElement('button');
                customSendBtn.type = 'button';
                customSendBtn.className = 'send-btn-modern';
                customSendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
                customSendBtn.onclick = function() {
                    if (messageInput.value.trim()) {
                        sendButton.click();
                    }
                };

                // Insérer le bouton personnalisé
                sendButton.parentNode.insertBefore(customSendBtn, sendButton);
            }

            // Initialiser les fonctionnalités LinkedIn
            if (window.messaging) {
                // Configurer l'ID de conversation actuel
                const conversationId = document.getElementById('<%= hdnConversationId.ClientID %>').value;
                if (conversationId) {
                    window.messaging.currentConversationId = conversationId;
                }

                // Configurer l'ID utilisateur actuel
                const currentUserId = document.getElementById('<%= hdnCurrentUserId.ClientID %>').value;
                if (currentUserId) {
                    window.messaging.currentUserId = currentUserId;
                }
            }
        });

        // Fonction pour mettre à jour l'interface après un postback
        function updateUIAfterPostback() {
            if (window.messaging) {
                window.messaging.scrollToBottom();
                window.messaging.handleTyping();
            }
        }

        // Appeler après chaque postback
        Sys.WebForms.PageRequestManager.getInstance().add_endRequest(updateUIAfterPostback);
    </script>
</asp:Content>
