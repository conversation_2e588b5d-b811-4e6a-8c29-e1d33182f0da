using System;
using System.Collections.Generic;

namespace LinCom.Classe
{
    public class LinkedInMessagingStats_Class
    {
        // Statistiques générales
        public int TotalConversations { get; set; }
        public int ActiveConversations { get; set; }
        public int ArchivedConversations { get; set; }
        public int PinnedConversations { get; set; }
        public int GroupConversations { get; set; }
        public int DirectConversations { get; set; }

        // Statistiques de messages
        public int TotalMessages { get; set; }
        public int UnreadMessages { get; set; }
        public int MessagesSentToday { get; set; }
        public int MessagesReceivedToday { get; set; }
        public int MessagesSentThisWeek { get; set; }
        public int MessagesReceivedThisWeek { get; set; }
        public int MessagesSentThisMonth { get; set; }
        public int MessagesReceivedThisMonth { get; set; }

        // Statistiques de réactions
        public int TotalReactions { get; set; }
        public int ReactionsReceived { get; set; }
        public int ReactionsGiven { get; set; }
        public int LikesReceived { get; set; }
        public int LovesReceived { get; set; }
        public int LaughsReceived { get; set; }

        // Statistiques de fichiers
        public int FilesShared { get; set; }
        public int FilesReceived { get; set; }
        public long TotalFileSize { get; set; }
        public int ImagesShared { get; set; }
        public int DocumentsShared { get; set; }
        public int VideosShared { get; set; }
        public int AudioFilesShared { get; set; }

        // Statistiques de temps
        public Nullable<DateTime> FirstMessageDate { get; set; }
        public Nullable<DateTime> LastMessageDate { get; set; }
        public Nullable<DateTime> LastActiveDate { get; set; }
        public double AverageResponseTime { get; set; } // En minutes
        public double TotalTimeSpent { get; set; } // En heures

        // Statistiques de contacts
        public int TotalContacts { get; set; }
        public int OnlineContacts { get; set; }
        public int RecentContacts { get; set; }
        public int BlockedContacts { get; set; }
        public int MutedConversations { get; set; }

        // Statistiques de notifications
        public int NotificationsSent { get; set; }
        public int NotificationsReceived { get; set; }
        public int NotificationsRead { get; set; }
        public int MentionsReceived { get; set; }
        public int MentionsRead { get; set; }

        // Statistiques de recherche
        public int SearchesPerformed { get; set; }
        public string MostSearchedTerm { get; set; }
        public int SearchResultsFound { get; set; }

        // Statistiques de présence
        public double OnlineTimeToday { get; set; } // En heures
        public double OnlineTimeThisWeek { get; set; }
        public double OnlineTimeThisMonth { get; set; }
        public Nullable<DateTime> LastSeenDate { get; set; }

        // Top statistiques
        public string MostActiveConversation { get; set; }
        public string MostActiveContact { get; set; }
        public string MostUsedEmoji { get; set; }
        public string MostUsedReaction { get; set; }
        public int LongestConversationDays { get; set; }

        // Statistiques par période
        public Dictionary<string, int> MessagesByDay { get; set; }
        public Dictionary<string, int> MessagesByHour { get; set; }
        public Dictionary<string, int> ConversationsByMonth { get; set; }

        // Statistiques de performance
        public double AverageMessagesPerDay { get; set; }
        public double AverageConversationsPerDay { get; set; }
        public double MessageDeliveryRate { get; set; }
        public double MessageReadRate { get; set; }

        public LinkedInMessagingStats_Class()
        {
            MessagesByDay = new Dictionary<string, int>();
            MessagesByHour = new Dictionary<string, int>();
            ConversationsByMonth = new Dictionary<string, int>();
            
            // Initialiser les valeurs par défaut
            TotalConversations = 0;
            ActiveConversations = 0;
            ArchivedConversations = 0;
            PinnedConversations = 0;
            GroupConversations = 0;
            DirectConversations = 0;
            
            TotalMessages = 0;
            UnreadMessages = 0;
            MessagesSentToday = 0;
            MessagesReceivedToday = 0;
            MessagesSentThisWeek = 0;
            MessagesReceivedThisWeek = 0;
            MessagesSentThisMonth = 0;
            MessagesReceivedThisMonth = 0;
            
            TotalReactions = 0;
            ReactionsReceived = 0;
            ReactionsGiven = 0;
            LikesReceived = 0;
            LovesReceived = 0;
            LaughsReceived = 0;
            
            FilesShared = 0;
            FilesReceived = 0;
            TotalFileSize = 0;
            ImagesShared = 0;
            DocumentsShared = 0;
            VideosShared = 0;
            AudioFilesShared = 0;
            
            AverageResponseTime = 0;
            TotalTimeSpent = 0;
            
            TotalContacts = 0;
            OnlineContacts = 0;
            RecentContacts = 0;
            BlockedContacts = 0;
            MutedConversations = 0;
            
            NotificationsSent = 0;
            NotificationsReceived = 0;
            NotificationsRead = 0;
            MentionsReceived = 0;
            MentionsRead = 0;
            
            SearchesPerformed = 0;
            SearchResultsFound = 0;
            
            OnlineTimeToday = 0;
            OnlineTimeThisWeek = 0;
            OnlineTimeThisMonth = 0;
            
            LongestConversationDays = 0;
            
            AverageMessagesPerDay = 0;
            AverageConversationsPerDay = 0;
            MessageDeliveryRate = 0;
            MessageReadRate = 0;
        }

        // Méthodes utilitaires
        public string GetFormattedFileSize()
        {
            if (TotalFileSize == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = TotalFileSize;
            int order = 0;
            
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            
            return $"{len:0.##} {sizes[order]}";
        }

        public string GetActivityLevel()
        {
            if (MessagesSentToday >= 50)
                return "Très actif";
            else if (MessagesSentToday >= 20)
                return "Actif";
            else if (MessagesSentToday >= 5)
                return "Modéré";
            else if (MessagesSentToday > 0)
                return "Faible";
            else
                return "Inactif";
        }

        public double GetResponseTimeInHours()
        {
            return AverageResponseTime / 60.0;
        }

        public string GetFormattedResponseTime()
        {
            if (AverageResponseTime < 1)
                return "< 1 min";
            else if (AverageResponseTime < 60)
                return $"{(int)AverageResponseTime} min";
            else if (AverageResponseTime < 1440) // 24 heures
                return $"{(int)(AverageResponseTime / 60)} h {(int)(AverageResponseTime % 60)} min";
            else
                return $"{(int)(AverageResponseTime / 1440)} j";
        }

        public double GetEngagementRate()
        {
            if (TotalMessages == 0) return 0;
            return (double)TotalReactions / TotalMessages * 100;
        }

        public double GetReadRate()
        {
            if (NotificationsReceived == 0) return 0;
            return (double)NotificationsRead / NotificationsReceived * 100;
        }

        public string GetMostActiveDay()
        {
            if (MessagesByDay.Count == 0) return "Aucun";
            
            var maxDay = "";
            var maxCount = 0;
            
            foreach (var day in MessagesByDay)
            {
                if (day.Value > maxCount)
                {
                    maxCount = day.Value;
                    maxDay = day.Key;
                }
            }
            
            return maxDay;
        }

        public string GetMostActiveHour()
        {
            if (MessagesByHour.Count == 0) return "Aucun";
            
            var maxHour = "";
            var maxCount = 0;
            
            foreach (var hour in MessagesByHour)
            {
                if (hour.Value > maxCount)
                {
                    maxCount = hour.Value;
                    maxHour = hour.Key;
                }
            }
            
            return maxHour + "h";
        }
    }
}
