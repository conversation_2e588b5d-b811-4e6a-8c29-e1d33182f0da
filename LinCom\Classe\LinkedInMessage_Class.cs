using LinCom.Model;
using System;
using System.Collections.Generic;

namespace LinCom.Classe
{
    public class LinkedInMessage_Class
    {
        public long MessageId { get; set; }
        public Nullable<long> ConversationId { get; set; }
        public Nullable<long> SenderId { get; set; }
        public string Contenu { get; set; }
        public string AttachmentUrl { get; set; }
        public Nullable<DateTime> DateEnvoi { get; set; }
        public string name { get; set; }

        // Propriétés étendues pour LinkedIn style
        public string MessageType { get; set; } // "text", "image", "file", "voice", "video"
        public string AttachmentName { get; set; }
        public string AttachmentType { get; set; }
        public long? AttachmentSize { get; set; }
        public string ThumbnailUrl { get; set; }
        public int? Duration { get; set; } // Pour les messages vocaux/vidéo en secondes

        // Propriétés pour l'affichage
        public string SenderName { get; set; }
        public string SenderPhoto { get; set; }
        public bool IsSentByCurrentUser { get; set; }
        public string FormattedTime { get; set; }
        public string TimeAgo { get; set; }

        // Statuts de message
        public string Status { get; set; } // "sent", "delivered", "read"
        public Nullable<DateTime> DeliveredAt { get; set; }
        public Nullable<DateTime> ReadAt { get; set; }
        public List<LinkedInMessageStatus_Class> ReadByUsers { get; set; }

        // Réactions
        public List<LinkedInMessageReaction_Class> Reactions { get; set; }
        public int LikeCount { get; set; }
        public int LoveCount { get; set; }
        public int LaughCount { get; set; }
        public bool HasUserReacted { get; set; }
        public string UserReactionType { get; set; }

        // Propriétés pour les réponses/threads
        public Nullable<long> ReplyToMessageId { get; set; }
        public string ReplyToContent { get; set; }
        public string ReplyToSender { get; set; }
        public List<LinkedInMessage_Class> Replies { get; set; }
        public int ReplyCount { get; set; }

        // Propriétés pour l'édition
        public bool IsEdited { get; set; }
        public Nullable<DateTime> EditedAt { get; set; }
        public string OriginalContent { get; set; }

        // Propriétés pour la suppression
        public bool IsDeleted { get; set; }
        public Nullable<DateTime> DeletedAt { get; set; }
        public Nullable<long> DeletedBy { get; set; }

        // Propriétés pour les mentions
        public List<long> MentionedUserIds { get; set; }
        public List<string> MentionedUserNames { get; set; }
        public bool HasMentions { get; set; }

        // Propriétés pour les liens
        public string LinkUrl { get; set; }
        public string LinkTitle { get; set; }
        public string LinkDescription { get; set; }
        public string LinkImage { get; set; }
        public bool HasLink { get; set; }

        // Propriétés pour la recherche
        public string SearchHighlight { get; set; }
        public bool IsSearchResult { get; set; }

        // Navigation properties
        public virtual Conversation Conversation { get; set; }
        public virtual Membre Sender { get; set; }
        public virtual ICollection<MessageStatu> MessageStatus { get; set; }

        public LinkedInMessage_Class()
        {
            DateEnvoi = DateTime.Now;
            MessageType = "text";
            Status = "sent";
            Reactions = new List<LinkedInMessageReaction_Class>();
            ReadByUsers = new List<LinkedInMessageStatus_Class>();
            Replies = new List<LinkedInMessage_Class>();
            MentionedUserIds = new List<long>();
            MentionedUserNames = new List<string>();
            MessageStatus = new HashSet<MessageStatu>();
            IsEdited = false;
            IsDeleted = false;
            HasMentions = false;
            HasLink = false;
            HasUserReacted = false;
            ReplyCount = 0;
            LikeCount = 0;
            LoveCount = 0;
            LaughCount = 0;
        }
    }
}
