/* LinkedIn-style Messaging CSS */
:root {
    --linkedin-blue: #0a66c2;
    --linkedin-blue-hover: #004182;
    --linkedin-gray-100: #f3f2ef;
    --linkedin-gray-200: #e9e5df;
    --linkedin-gray-300: #d4d2ce;
    --linkedin-gray-400: #a8a5a0;
    --linkedin-gray-500: #8d8b87;
    --linkedin-gray-600: #6f6d69;
    --linkedin-gray-700: #54524f;
    --linkedin-gray-800: #3b3938;
    --linkedin-gray-900: #1d1c1a;
    --linkedin-white: #ffffff;
    --linkedin-green: #057642;
    --linkedin-red: #cc1016;
    --linkedin-orange: #f5820d;
    
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--linkedin-gray-100);
}

/* Main Container */
.linkedin-messaging-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--linkedin-white);
}

.messaging-layout {
    display: flex;
    height: calc(100vh - 60px); /* Adjust for header */
    background-color: var(--linkedin-white);
}

/* Left Sidebar - Conversations */
.conversations-sidebar {
    width: 360px;
    background-color: var(--linkedin-white);
    border-right: 1px solid var(--linkedin-gray-200);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--linkedin-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--linkedin-white);
}

.header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: 600;
    color: var(--linkedin-gray-900);
}

.header-title i {
    color: var(--linkedin-blue);
}

.header-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--border-radius-md);
    background-color: transparent;
    color: var(--linkedin-gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: var(--linkedin-gray-100);
    color: var(--linkedin-gray-900);
}

/* Search Container */
.search-container {
    padding: 12px 20px;
    border-bottom: 1px solid var(--linkedin-gray-200);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background-color: var(--linkedin-gray-100);
    border-radius: var(--border-radius-lg);
    padding: 8px 12px;
}

.search-icon {
    color: var(--linkedin-gray-500);
    margin-right: 8px;
    font-size: 14px;
}

.search-input-modern {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 14px;
    color: var(--linkedin-gray-900);
    font-family: inherit;
}

.search-input-modern::placeholder {
    color: var(--linkedin-gray-500);
}

.search-filter-btn {
    background: none;
    border: none;
    color: var(--linkedin-gray-500);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.search-filter-btn:hover {
    color: var(--linkedin-blue);
    background-color: var(--linkedin-gray-200);
}

/* Filter Tabs */
.filter-tabs {
    display: flex;
    padding: 0 20px;
    border-bottom: 1px solid var(--linkedin-gray-200);
    background-color: var(--linkedin-white);
}

.filter-tab {
    background: none;
    border: none;
    padding: 12px 0;
    margin-right: 24px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--linkedin-gray-600);
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.filter-tab.active {
    color: var(--linkedin-blue);
    border-bottom-color: var(--linkedin-blue);
}

.filter-tab:hover {
    color: var(--linkedin-gray-900);
}

.tab-count {
    background-color: var(--linkedin-gray-200);
    color: var(--linkedin-gray-700);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
}

.filter-tab.active .tab-count {
    background-color: var(--linkedin-blue);
    color: var(--linkedin-white);
}

/* Conversations List */
.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.conversation-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.conversation-item:hover {
    background-color: var(--linkedin-gray-100);
    text-decoration: none;
    color: inherit;
}

.conversation-item.active {
    background-color: var(--linkedin-blue);
    color: var(--linkedin-white);
    border-left-color: var(--linkedin-blue);
}

.conversation-item.has-unread {
    background-color: #f8f9ff;
    border-left-color: var(--linkedin-blue);
}

.conversation-avatar {
    position: relative;
    margin-right: 12px;
    flex-shrink: 0;
}

.conversation-avatar img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--linkedin-white);
}

.online-status {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--linkedin-white);
}

.online-status.online {
    background-color: var(--linkedin-green);
}

.online-status.offline {
    background-color: var(--linkedin-gray-400);
}

.conversation-content {
    flex: 1;
    min-width: 0;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.contact-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--linkedin-gray-900);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.last-message-time {
    font-size: 12px;
    color: var(--linkedin-gray-500);
    flex-shrink: 0;
}

.conversation-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.last-message {
    font-size: 13px;
    color: var(--linkedin-gray-600);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.conversation-badges {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
}

.unread-count {
    background-color: var(--linkedin-blue);
    color: var(--linkedin-white);
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.pin-icon {
    color: var(--linkedin-gray-500);
    font-size: 12px;
}

/* Typing Indicator */
.conversation-indicators {
    margin-top: 4px;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 2px;
    color: var(--linkedin-blue);
    font-size: 10px;
}

.typing-indicator i {
    animation: typing-pulse 1.4s infinite;
}

.typing-indicator i:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator i:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing-pulse {
    0%, 60%, 100% {
        opacity: 0.3;
    }
    30% {
        opacity: 1;
    }
}

/* Empty State */
.empty-conversations {
    text-align: center;
    padding: 40px 20px;
    color: var(--linkedin-gray-500);
}

.empty-conversations i {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--linkedin-gray-300);
}

.empty-conversations p {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
}

.empty-conversations small {
    font-size: 14px;
}

/* Main Chat Area */
.chat-main-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--linkedin-white);
    overflow: hidden;
}

/* Chat Header */
.chat-header-modern {
    padding: 16px 24px;
    border-bottom: 1px solid var(--linkedin-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--linkedin-white);
    z-index: 10;
}

.chat-participant-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.participant-avatar {
    position: relative;
}

.participant-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.participant-details h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--linkedin-gray-900);
}

.participant-status {
    font-size: 12px;
    color: var(--linkedin-green);
    font-weight: 500;
}

.chat-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--border-radius-md);
    background-color: transparent;
    color: var(--linkedin-gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.chat-action-btn:hover {
    background-color: var(--linkedin-gray-100);
    color: var(--linkedin-gray-900);
}

/* Messages Container */
.messages-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.messages-scroll-area {
    height: 100%;
    overflow-y: auto;
    padding: 16px 24px;
    scroll-behavior: smooth;
}

/* Message Groups */
.message-group {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-end;
}

.message-group.sent {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    margin: 0 8px;
    flex-shrink: 0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.message-content {
    max-width: 70%;
    display: flex;
    flex-direction: column;
}

.message-group.sent .message-content {
    align-items: flex-end;
}

/* Message Bubbles */
.message-bubble {
    background-color: var(--linkedin-gray-100);
    border-radius: var(--border-radius-xl);
    padding: 12px 16px;
    position: relative;
    word-wrap: break-word;
    max-width: 100%;
}

.message-group.sent .message-bubble {
    background-color: var(--linkedin-blue);
    color: var(--linkedin-white);
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
}

/* Message Meta */
.message-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
    font-size: 11px;
    color: var(--linkedin-gray-500);
}

.message-group.sent .message-meta {
    justify-content: flex-end;
}

.message-time {
    white-space: nowrap;
}

.message-status {
    color: var(--linkedin-blue);
}

/* Message Input Area */
.message-input-area {
    border-top: 1px solid var(--linkedin-gray-200);
    background-color: var(--linkedin-white);
    padding: 16px 24px;
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background-color: var(--linkedin-gray-100);
    border-radius: var(--border-radius-xl);
    padding: 8px;
}

.input-toolbar {
    display: flex;
    gap: 4px;
}

.toolbar-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--border-radius-md);
    background-color: transparent;
    color: var(--linkedin-gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background-color: var(--linkedin-gray-200);
    color: var(--linkedin-gray-900);
}

.message-input-wrapper {
    flex: 1;
    position: relative;
}

.message-input-modern {
    width: 100%;
    border: none;
    background: transparent;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.4;
    padding: 8px 12px;
    font-family: inherit;
    color: var(--linkedin-gray-900);
    max-height: 120px;
    min-height: 20px;
}

.message-input-modern::placeholder {
    color: var(--linkedin-gray-500);
}

.send-btn-modern {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background-color: var(--linkedin-blue);
    color: var(--linkedin-white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 14px;
}

.send-btn-modern:hover {
    background-color: var(--linkedin-blue-hover);
    transform: scale(1.05);
}

.send-btn-modern:disabled {
    background-color: var(--linkedin-gray-300);
    cursor: not-allowed;
    transform: none;
}

.character-counter {
    text-align: right;
    margin-top: 4px;
    font-size: 11px;
    color: var(--linkedin-gray-500);
}

/* Responsive Design */
@media (max-width: 768px) {
    .messaging-layout {
        flex-direction: column;
    }
    
    .conversations-sidebar {
        width: 100%;
        height: 40%;
        border-right: none;
        border-bottom: 1px solid var(--linkedin-gray-200);
    }
    
    .chat-main-area {
        height: 60%;
    }
    
    .message-content {
        max-width: 85%;
    }
}

@media (max-width: 480px) {
    .sidebar-header,
    .chat-header-modern,
    .message-input-area {
        padding: 12px 16px;
    }

    .messages-scroll-area {
        padding: 12px 16px;
    }

    .conversation-item {
        padding: 10px 16px;
    }
}

/* Message Reactions */
.message-reactions {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message-group:hover .message-reactions {
    opacity: 1;
}

.reaction-btn {
    background: var(--linkedin-gray-100);
    border: 1px solid var(--linkedin-gray-200);
    border-radius: 12px;
    padding: 2px 6px;
    font-size: 11px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 2px;
    transition: all 0.2s ease;
}

.reaction-btn:hover {
    background: var(--linkedin-gray-200);
}

.reaction-btn.active {
    background: var(--linkedin-blue);
    color: var(--linkedin-white);
    border-color: var(--linkedin-blue);
}

.add-reaction-btn {
    background: transparent;
    border: 1px solid var(--linkedin-gray-300);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--linkedin-gray-500);
    transition: all 0.2s ease;
}

.add-reaction-btn:hover {
    background: var(--linkedin-gray-100);
    color: var(--linkedin-gray-700);
}

/* Emoji Picker Modal */
.emoji-picker-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.emoji-picker-content {
    background: var(--linkedin-white);
    border-radius: var(--border-radius-lg);
    width: 400px;
    height: 300px;
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.emoji-picker-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--linkedin-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.emoji-categories {
    display: flex;
    gap: 8px;
}

.emoji-category {
    background: none;
    border: none;
    padding: 8px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 18px;
    transition: background 0.2s ease;
}

.emoji-category:hover,
.emoji-category.active {
    background: var(--linkedin-gray-100);
}

.close-emoji-btn {
    background: none;
    border: none;
    color: var(--linkedin-gray-500);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.close-emoji-btn:hover {
    background: var(--linkedin-gray-100);
    color: var(--linkedin-gray-700);
}

.emoji-grid {
    flex: 1;
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    overflow-y: auto;
}

.emoji-item {
    padding: 8px;
    text-align: center;
    cursor: pointer;
    border-radius: var(--border-radius-md);
    font-size: 20px;
    transition: all 0.2s ease;
    user-select: none;
}

.emoji-item:hover {
    background: var(--linkedin-gray-100);
    transform: scale(1.1);
}

/* Voice Message */
.voice-message-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: var(--linkedin-gray-200);
    color: var(--linkedin-gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.voice-message-btn:hover {
    background: var(--linkedin-gray-300);
}

.voice-message-btn.recording {
    background: var(--linkedin-red);
    color: var(--linkedin-white);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Attachment Preview */
.attachment-preview {
    background: var(--linkedin-gray-100);
    border: 1px solid var(--linkedin-gray-200);
    border-radius: var(--border-radius-md);
    padding: 12px;
    margin-bottom: 8px;
    display: none;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    background: var(--linkedin-white);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--linkedin-gray-200);
    margin-bottom: 8px;
}

.attachment-item:last-child {
    margin-bottom: 0;
}

.file-icon {
    font-size: 24px;
    color: var(--linkedin-blue);
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    display: block;
    font-weight: 500;
    color: var(--linkedin-gray-900);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    display: block;
    font-size: 12px;
    color: var(--linkedin-gray-500);
}

.remove-file-btn {
    background: var(--linkedin-red);
    color: var(--linkedin-white);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.remove-file-btn:hover {
    background: #a00;
}

/* Message Images */
.message-image {
    max-width: 250px;
    max-height: 200px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: transform 0.2s ease;
    margin: 4px 0;
}

.message-image:hover {
    transform: scale(1.02);
}

/* Message Links */
.message-link {
    color: var(--linkedin-blue);
    text-decoration: none;
    font-weight: 500;
}

.message-link:hover {
    text-decoration: underline;
}

/* Quick Actions Bar */
.quick-actions-bar {
    display: flex;
    gap: 8px;
    padding: 8px 0;
    border-bottom: 1px solid var(--linkedin-gray-200);
    margin-bottom: 8px;
}

.quick-action-btn {
    background: var(--linkedin-gray-100);
    border: 1px solid var(--linkedin-gray-200);
    border-radius: var(--border-radius-md);
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--linkedin-gray-700);
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    background: var(--linkedin-gray-200);
    color: var(--linkedin-gray-900);
}

/* Chat Info Sidebar */
.chat-info-sidebar {
    width: 300px;
    background: var(--linkedin-white);
    border-left: 1px solid var(--linkedin-gray-200);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.info-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--linkedin-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--linkedin-gray-900);
}

.close-info-btn {
    background: none;
    border: none;
    color: var(--linkedin-gray-500);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.close-info-btn:hover {
    background: var(--linkedin-gray-100);
    color: var(--linkedin-gray-700);
}

.info-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;
}

.info-section {
    margin-bottom: 24px;
}

.info-section h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--linkedin-gray-900);
}

.participant-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--linkedin-gray-100);
    border-radius: var(--border-radius-md);
}

.participant-card img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.participant-card h6 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--linkedin-gray-900);
}

.participant-card p {
    margin: 0;
    font-size: 12px;
    color: var(--linkedin-gray-600);
}

/* Settings Toggle */
.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--linkedin-gray-200);
}

.setting-item:last-child {
    border-bottom: none;
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--linkedin-gray-300);
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--linkedin-blue);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: var(--border-radius-md);
    color: var(--linkedin-white);
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.notification-success {
    background: var(--linkedin-green);
}

.notification-error {
    background: var(--linkedin-red);
}

.notification-info {
    background: var(--linkedin-blue);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Typing Animation in Messages */
.typing-bubble {
    background: var(--linkedin-gray-200);
    padding: 12px 16px;
    border-radius: var(--border-radius-xl);
    display: inline-block;
}

.typing-animation {
    display: flex;
    gap: 4px;
}

.typing-animation span {
    width: 6px;
    height: 6px;
    background: var(--linkedin-gray-500);
    border-radius: 50%;
    animation: typing-bounce 1.4s infinite;
}

.typing-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing-bounce {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Dropdown Menu */
.dropdown-menu {
    background: var(--linkedin-white);
    border: 1px solid var(--linkedin-gray-200);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    padding: 8px 0;
    min-width: 160px;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    color: var(--linkedin-gray-700);
    text-decoration: none;
    font-size: 14px;
    transition: background 0.2s ease;
}

.dropdown-item:hover {
    background: var(--linkedin-gray-100);
    color: var(--linkedin-gray-900);
    text-decoration: none;
}

.dropdown-item.text-danger {
    color: var(--linkedin-red);
}

.dropdown-divider {
    height: 1px;
    background: var(--linkedin-gray-200);
    margin: 4px 0;
    border: none;
}

/* Scrollbar Styling */
.conversations-list::-webkit-scrollbar,
.messages-scroll-area::-webkit-scrollbar,
.emoji-grid::-webkit-scrollbar,
.info-content::-webkit-scrollbar {
    width: 6px;
}

.conversations-list::-webkit-scrollbar-track,
.messages-scroll-area::-webkit-scrollbar-track,
.emoji-grid::-webkit-scrollbar-track,
.info-content::-webkit-scrollbar-track {
    background: var(--linkedin-gray-100);
}

.conversations-list::-webkit-scrollbar-thumb,
.messages-scroll-area::-webkit-scrollbar-thumb,
.emoji-grid::-webkit-scrollbar-thumb,
.info-content::-webkit-scrollbar-thumb {
    background: var(--linkedin-gray-300);
    border-radius: 3px;
}

.conversations-list::-webkit-scrollbar-thumb:hover,
.messages-scroll-area::-webkit-scrollbar-thumb:hover,
.emoji-grid::-webkit-scrollbar-thumb:hover,
.info-content::-webkit-scrollbar-thumb:hover {
    background: var(--linkedin-gray-400);
}
