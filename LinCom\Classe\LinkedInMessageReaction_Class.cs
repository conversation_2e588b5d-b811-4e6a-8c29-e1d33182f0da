using LinCom.Model;
using System;

namespace LinCom.Classe
{
    public class LinkedInMessageReaction_Class
    {
        public long ReactionId { get; set; }
        public long MessageId { get; set; }
        public long UserId { get; set; }
        public string ReactionType { get; set; } // "like", "love", "laugh", "wow", "sad", "angry"
        public Nullable<DateTime> CreatedAt { get; set; }
        public Nullable<DateTime> UpdatedAt { get; set; }

        // Propriétés pour l'affichage
        public string UserName { get; set; }
        public string UserPhoto { get; set; }
        public string ReactionEmoji { get; set; }
        public string ReactionText { get; set; }
        public string ReactionColor { get; set; }

        // Propriétés pour les statistiques
        public bool IsActive { get; set; }
        public int ReactionCount { get; set; }
        public bool IsCurrentUserReaction { get; set; }

        // Navigation properties
        public virtual Message Message { get; set; }
        public virtual Membre User { get; set; }

        public LinkedInMessageReaction_Class()
        {
            CreatedAt = DateTime.Now;
            IsActive = true;
            ReactionCount = 1;
            IsCurrentUserReaction = false;
        }

        // Méthodes utilitaires
        public string GetReactionEmoji()
        {
            switch (ReactionType?.ToLower())
            {
                case "like":
                    return "👍";
                case "love":
                    return "❤️";
                case "laugh":
                    return "😂";
                case "wow":
                    return "😮";
                case "sad":
                    return "😢";
                case "angry":
                    return "😠";
                default:
                    return "👍";
            }
        }

        public string GetReactionText()
        {
            switch (ReactionType?.ToLower())
            {
                case "like":
                    return "J'aime";
                case "love":
                    return "J'adore";
                case "laugh":
                    return "Haha";
                case "wow":
                    return "Wow";
                case "sad":
                    return "Triste";
                case "angry":
                    return "Grrr";
                default:
                    return "J'aime";
            }
        }

        public string GetReactionColor()
        {
            switch (ReactionType?.ToLower())
            {
                case "like":
                    return "#0a66c2"; // Bleu LinkedIn
                case "love":
                    return "#e74c3c"; // Rouge
                case "laugh":
                    return "#f39c12"; // Orange
                case "wow":
                    return "#f1c40f"; // Jaune
                case "sad":
                    return "#3498db"; // Bleu clair
                case "angry":
                    return "#e67e22"; // Orange foncé
                default:
                    return "#0a66c2";
            }
        }

        public string GetTimeAgo()
        {
            if (!CreatedAt.HasValue)
                return "";

            var timeSpan = DateTime.Now - CreatedAt.Value;

            if (timeSpan.TotalMinutes < 1)
                return "À l'instant";
            else if (timeSpan.TotalMinutes < 60)
                return $"Il y a {(int)timeSpan.TotalMinutes} min";
            else if (timeSpan.TotalHours < 24)
                return $"Il y a {(int)timeSpan.TotalHours}h";
            else if (timeSpan.TotalDays < 7)
                return $"Il y a {(int)timeSpan.TotalDays}j";
            else
                return CreatedAt.Value.ToString("dd/MM/yyyy");
        }

        public bool IsRecent()
        {
            if (!CreatedAt.HasValue)
                return false;

            return (DateTime.Now - CreatedAt.Value).TotalMinutes < 5;
        }

        public void UpdateReaction(string newReactionType)
        {
            ReactionType = newReactionType;
            UpdatedAt = DateTime.Now;
        }

        public void ToggleActive()
        {
            IsActive = !IsActive;
            UpdatedAt = DateTime.Now;
        }
    }
}
