using LinCom.Model;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;

namespace LinCom.Classe
{
    /// <summary>
    /// Gestionnaire pour les fonctionnalités avancées de messagerie style LinkedIn
    /// Suit le pattern des autres classes du projet LinCom
    /// </summary>
    public class LinkedInMessagingManager_Class
    {
        private readonly ILinkedInMessaging _linkedInMessagingService;

        public LinkedInMessagingManager_Class()
        {
            _linkedInMessagingService = new LinkedInMessagingImp();
        }

        #region Méthodes publiques principales

        /// <summary>
        /// Charge les conversations avec détails dans un ListView
        /// </summary>
        public void ChargerConversationsAvecDetails(ListView listView, long userId)
        {
            _linkedInMessagingService.ChargerConversationsAvecDetails(listView, userId);
        }

        /// <summary>
        /// Charge les messages avec détails dans un Repeater
        /// </summary>
        public void ChargerMessagesAvecDetails(Repeater rpt, long conversationId, int nombreMessages = 50)
        {
            _linkedInMessagingService.ChargerMessagesAvecDetails(rpt, conversationId, nombreMessages);
        }

        /// <summary>
        /// Envoie un message avec statut
        /// </summary>
        public int EnvoyerMessageAvecStatut(LinkedInMessage_Class message)
        {
            return _linkedInMessagingService.EnvoyerMessageAvecStatut(message);
        }

        /// <summary>
        /// Recherche dans les conversations
        /// </summary>
        public void RechercherConversations(ListView listView, long userId, string searchTerm)
        {
            _linkedInMessagingService.RechercherConversations(listView, userId, searchTerm);
        }

        /// <summary>
        /// Filtre les conversations
        /// </summary>
        public void FiltrerConversations(ListView listView, long userId, string filterType)
        {
            _linkedInMessagingService.FiltrerConversations(listView, userId, filterType);
        }

        /// <summary>
        /// Obtient les statistiques de messagerie
        /// </summary>
        public void ObtenirStatistiquesMessagerie(long userId, LinkedInMessagingStats_Class statsClass)
        {
            _linkedInMessagingService.ObtenirStatistiquesMessagerie(userId, statsClass);
        }

        #endregion

        #region Méthodes utilitaires

        /// <summary>
        /// Obtient les conversations d'un utilisateur avec toutes les informations nécessaires
        /// </summary>
        public List<LinkedInConversation_Class> ObtenirConversationsAvecDetails(long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var conversations = (from c in context.Conversations
                                       join pc in context.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                       where pc.MembreId == userId
                                       select new LinkedInConversation_Class
                                       {
                                           ConversationId = c.ConversationId,
                                           Sujet = c.Sujet,
                                           IsGroup = c.IsGroup,
                                           CreatedAt = c.CreatedAt,
                                           LastActivity = c.LastActivity,
                                           CreatedBy = c.CreatedBy
                                       }).ToList();

                    // Enrichir chaque conversation avec des détails supplémentaires
                    foreach (var conv in conversations)
                    {
                        EnrichirConversationAvecDetails(conv, userId);
                    }

                    return conversations.OrderByDescending(c => c.LastActivity).ToList();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'obtention des conversations: {ex.Message}");
                return new List<LinkedInConversation_Class>();
            }
        }

        /// <summary>
        /// Enrichit une conversation avec des détails supplémentaires
        /// </summary>
        private void EnrichirConversationAvecDetails(LinkedInConversation_Class conversation, long userId)
        {
            try
            {
                // Obtenir les participants
                conversation.Participants = ObtenirParticipantsConversation(conversation.ConversationId, userId);

                // Obtenir le dernier message
                var lastMessage = ObtenirDernierMessage(conversation.ConversationId);
                if (lastMessage != null)
                {
                    conversation.LastMessage = lastMessage.Contenu;
                    conversation.LastMessageTime = lastMessage.DateEnvoi;
                }

                // Compter les messages non lus
                conversation.UnreadCount = CompterMessagesNonLus(conversation.ConversationId, userId);
                conversation.HasUnread = conversation.UnreadCount > 0;

                // Vérifier les statuts
                conversation.IsPinned = VerifierConversationEpinglee(conversation.ConversationId, userId);
                conversation.IsTyping = VerifierSiQuelquunTape(conversation.ConversationId, userId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'enrichissement de la conversation: {ex.Message}");
            }
        }

        #endregion

        #region Méthodes privées

        /// <summary>
        /// Obtient les participants d'une conversation
        /// </summary>
        private List<LinkedInParticipant_Class> ObtenirParticipantsConversation(long conversationId, long currentUserId)
        {
            try
            {
                using (var context = new Connection())
                {
                    return (from pc in context.ParticipantConversations
                           join m in context.Membres on pc.MembreId equals m.MembreId
                           where pc.ConversationId == conversationId && pc.MembreId != currentUserId
                           select new LinkedInParticipant_Class
                           {
                               UserId = m.MembreId,
                               Name = m.Nom + " " + m.Prenom,
                               FirstName = m.Prenom,
                               LastName = m.Nom,
                               Email = m.Email,
                               PhotoUrl = m.PhotoProfil ?? "default-avatar.png",
                               Title = m.Titre ?? "",
                               IsOnline = ObtenirStatutUtilisateurEnLigne(m.MembreId),
                               LastSeen = ObtenirDernierVuUtilisateur(m.MembreId),
                               JoinedAt = pc.JoinedAt
                           }).ToList();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'obtention des participants: {ex.Message}");
                return new List<LinkedInParticipant_Class>();
            }
        }

        /// <summary>
        /// Obtient le dernier message d'une conversation
        /// </summary>
        private LinkedInMessage_Class ObtenirDernierMessage(long conversationId)
        {
            try
            {
                using (var context = new Connection())
                {
                    var lastMessage = (from m in context.Messages
                                     join sender in context.Membres on m.SenderId equals sender.MembreId
                                     where m.ConversationId == conversationId
                                     orderby m.DateEnvoi descending
                                     select new LinkedInMessage_Class
                                     {
                                         MessageId = m.MessageId,
                                         ConversationId = m.ConversationId,
                                         SenderId = m.SenderId,
                                         Contenu = m.Contenu,
                                         AttachmentUrl = m.AttachmentUrl,
                                         DateEnvoi = m.DateEnvoi,
                                         name = m.name,
                                         SenderName = sender.Nom + " " + sender.Prenom,
                                         SenderPhoto = sender.PhotoProfil ?? "default-avatar.png",
                                         AttachmentType = ObtenirTypeAttachementDepuisUrl(m.AttachmentUrl)
                                     }).FirstOrDefault();

                    return lastMessage;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'obtention du dernier message: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Compte les messages non lus pour un utilisateur dans une conversation
        /// </summary>
        private int CompterMessagesNonLus(long conversationId, long userId)
        {
            try
            {
                using (var context = new Connection())
                {
                    return (from ms in context.MessageStatus
                           join m in context.Messages on ms.MessageId equals m.MessageId
                           where m.ConversationId == conversationId &&
                                 ms.UserId == userId &&
                                 ms.IsRead == 0
                           select ms).Count();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du comptage des messages non lus: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Vérifie si une conversation est épinglée pour un utilisateur
        /// </summary>
        private bool VerifierConversationEpinglee(long conversationId, long userId)
        {
            // À implémenter avec une table ConversationSettings
            return false;
        }

        /// <summary>
        /// Vérifie si quelqu'un est en train de taper dans la conversation
        /// </summary>
        private bool VerifierSiQuelquunTape(long conversationId, long currentUserId)
        {
            // À implémenter avec SignalR ou cache Redis
            return false;
        }

        /// <summary>
        /// Obtient le statut en ligne d'un utilisateur
        /// </summary>
        private bool ObtenirStatutUtilisateurEnLigne(long userId)
        {
            // À implémenter avec un système de présence
            var lastSeen = ObtenirDernierVuUtilisateur(userId);
            return lastSeen.HasValue && (DateTime.Now - lastSeen.Value).TotalMinutes < 5;
        }

        /// <summary>
        /// Obtient la dernière fois qu'un utilisateur a été vu
        /// </summary>
        private DateTime? ObtenirDernierVuUtilisateur(long userId)
        {
            // À implémenter avec une table UserPresence
            return null;
        }

        /// <summary>
        /// Détermine le type de pièce jointe à partir de l'URL
        /// </summary>
        private string ObtenirTypeAttachementDepuisUrl(string attachmentUrl)
        {
            if (string.IsNullOrEmpty(attachmentUrl)) return "";

            var extension = System.IO.Path.GetExtension(attachmentUrl).ToLower();

            switch (extension)
            {
                case ".jpg":
                case ".jpeg":
                case ".png":
                case ".gif":
                case ".webp":
                    return "image";
                case ".pdf":
                    return "pdf";
                case ".doc":
                case ".docx":
                    return "document";
                case ".mp3":
                case ".wav":
                case ".ogg":
                    return "audio";
                case ".mp4":
                case ".avi":
                case ".mov":
                    return "video";
                default:
                    return "file";
            }
        }

        #endregion
    }
}
