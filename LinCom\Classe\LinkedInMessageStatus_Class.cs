using LinCom.Model;
using System;

namespace LinCom.Classe
{
    public class LinkedInMessageStatus_Class
    {
        public long MessageStatusId { get; set; }
        public long MessageId { get; set; }
        public long UserId { get; set; }
        public int IsRead { get; set; }
        public Nullable<DateTime> ReadAt { get; set; }

        // Propriétés étendues pour LinkedIn style
        public int IsDelivered { get; set; }
        public Nullable<DateTime> DeliveredAt { get; set; }
        public int IsReceived { get; set; }
        public Nullable<DateTime> ReceivedAt { get; set; }

        // Informations sur l'appareil/plateforme
        public string DeviceType { get; set; } // "web", "mobile", "desktop"
        public string Platform { get; set; } // "android", "ios", "windows", "web"
        public string UserAgent { get; set; }
        public string IpAddress { get; set; }

        // Informations de notification
        public bool NotificationSent { get; set; }
        public Nullable<DateTime> NotificationSentAt { get; set; }
        public string NotificationType { get; set; } // "push", "email", "sms"
        public bool NotificationRead { get; set; }
        public Nullable<DateTime> NotificationReadAt { get; set; }

        // Statut de réaction
        public bool HasReacted { get; set; }
        public string ReactionType { get; set; } // "like", "love", "laugh", "angry", "sad"
        public Nullable<DateTime> ReactionAt { get; set; }

        // Informations de réponse
        public bool HasReplied { get; set; }
        public Nullable<DateTime> RepliedAt { get; set; }
        public long? ReplyMessageId { get; set; }

        // Statut de mention
        public bool IsMentioned { get; set; }
        public bool MentionRead { get; set; }
        public Nullable<DateTime> MentionReadAt { get; set; }

        // Informations de partage
        public bool HasShared { get; set; }
        public Nullable<DateTime> SharedAt { get; set; }
        public string SharePlatform { get; set; }

        // Statut de suppression
        public bool IsDeleted { get; set; }
        public Nullable<DateTime> DeletedAt { get; set; }
        public string DeleteReason { get; set; }

        // Propriétés pour l'affichage
        public string UserName { get; set; }
        public string UserPhoto { get; set; }
        public string StatusText { get; set; }
        public string StatusIcon { get; set; }
        public string StatusColor { get; set; }

        // Navigation properties
        public virtual Message Message { get; set; }
        public virtual Membre User { get; set; }

        public LinkedInMessageStatus_Class()
        {
            IsRead = 0;
            IsDelivered = 0;
            IsReceived = 0;
            NotificationSent = false;
            NotificationRead = false;
            HasReacted = false;
            HasReplied = false;
            IsMentioned = false;
            MentionRead = false;
            HasShared = false;
            IsDeleted = false;
            DeviceType = "web";
            Platform = "web";
            NotificationType = "push";
        }

        // Méthodes utilitaires
        public string GetStatusText()
        {
            if (IsDeleted)
                return "Supprimé";
            
            if (IsRead == 1)
                return "Lu";
            
            if (IsDelivered == 1)
                return "Livré";
            
            if (IsReceived == 1)
                return "Reçu";
            
            return "Envoyé";
        }

        public string GetStatusIcon()
        {
            if (IsDeleted)
                return "fas fa-trash";
            
            if (IsRead == 1)
                return "fas fa-check-double text-primary";
            
            if (IsDelivered == 1)
                return "fas fa-check-double";
            
            if (IsReceived == 1)
                return "fas fa-check";
            
            return "fas fa-clock";
        }

        public string GetStatusColor()
        {
            if (IsDeleted)
                return "#dc3545"; // Rouge
            
            if (IsRead == 1)
                return "#0a66c2"; // Bleu LinkedIn
            
            if (IsDelivered == 1)
                return "#28a745"; // Vert
            
            if (IsReceived == 1)
                return "#6c757d"; // Gris
            
            return "#ffc107"; // Jaune
        }

        public string GetTimeAgo()
        {
            DateTime? referenceTime = ReadAt ?? DeliveredAt ?? ReceivedAt;
            
            if (!referenceTime.HasValue)
                return "";

            var timeSpan = DateTime.Now - referenceTime.Value;

            if (timeSpan.TotalMinutes < 1)
                return "À l'instant";
            else if (timeSpan.TotalMinutes < 60)
                return $"Il y a {(int)timeSpan.TotalMinutes} min";
            else if (timeSpan.TotalHours < 24)
                return $"Il y a {(int)timeSpan.TotalHours}h";
            else if (timeSpan.TotalDays < 7)
                return $"Il y a {(int)timeSpan.TotalDays}j";
            else
                return referenceTime.Value.ToString("dd/MM/yyyy");
        }

        public bool IsRecentActivity()
        {
            DateTime? lastActivity = ReadAt ?? DeliveredAt ?? ReceivedAt;
            
            if (!lastActivity.HasValue)
                return false;

            return (DateTime.Now - lastActivity.Value).TotalMinutes < 5;
        }

        public void MarkAsRead()
        {
            IsRead = 1;
            ReadAt = DateTime.Now;
            
            if (IsMentioned && !MentionRead)
            {
                MentionRead = true;
                MentionReadAt = DateTime.Now;
            }
        }

        public void MarkAsDelivered()
        {
            IsDelivered = 1;
            DeliveredAt = DateTime.Now;
        }

        public void MarkAsReceived()
        {
            IsReceived = 1;
            ReceivedAt = DateTime.Now;
        }

        public void AddReaction(string reactionType)
        {
            HasReacted = true;
            ReactionType = reactionType;
            ReactionAt = DateTime.Now;
        }

        public void RemoveReaction()
        {
            HasReacted = false;
            ReactionType = null;
            ReactionAt = null;
        }
    }
}
