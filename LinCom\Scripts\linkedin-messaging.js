// LinkedIn-style Messaging JavaScript
class LinkedInMessaging {
    constructor() {
        this.currentConversationId = null;
        this.typingTimer = null;
        this.isTyping = false;
        this.lastMessageId = null;
        this.emojiData = {};
        this.attachedFiles = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadEmojiData();
        this.setupAutoRefresh();
        this.setupTypingIndicator();
        this.setupFileUpload();
        this.setupKeyboardShortcuts();
    }

    setupEventListeners() {
        // Message input events
        const messageInput = document.getElementById('txtMessage');
        if (messageInput) {
            messageInput.addEventListener('input', () => this.handleTyping());
            messageInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
            messageInput.addEventListener('paste', (e) => this.handlePaste(e));
        }

        // Toolbar buttons
        document.getElementById('btnEmoji')?.addEventListener('click', () => this.toggleEmojiPicker());
        document.getElementById('btnAttachment')?.addEventListener('click', () => this.openFileDialog());
        document.getElementById('btnGif')?.addEventListener('click', () => this.openGifPicker());
        document.getElementById('btnVoiceMessage')?.addEventListener('click', () => this.startVoiceRecording());

        // Filter tabs
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.filterConversations(e.target.dataset.filter));
        });

        // Emoji picker events
        document.querySelectorAll('.emoji-category').forEach(category => {
            category.addEventListener('click', (e) => this.switchEmojiCategory(e.target.dataset.category));
        });

        // Message reactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleReaction(e.target.closest('.reaction-btn'));
            }
            if (e.target.closest('.add-reaction-btn')) {
                this.showReactionPicker(e.target.closest('.add-reaction-btn'));
            }
        });

        // Chat actions
        document.querySelectorAll('.chat-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleChatAction(e.target.closest('.chat-action-btn')));
        });

        // Auto-scroll to bottom
        this.scrollToBottom();
    }

    handleTyping() {
        const messageInput = document.getElementById('txtMessage');
        const charCount = document.getElementById('charCount');
        
        // Update character counter
        if (charCount && messageInput) {
            const length = messageInput.value.length;
            charCount.textContent = `${length}/1000`;
            
            if (length > 900) {
                charCount.style.color = 'var(--linkedin-red)';
            } else if (length > 800) {
                charCount.style.color = 'var(--linkedin-orange)';
            } else {
                charCount.style.color = 'var(--linkedin-gray-500)';
            }
        }

        // Handle typing indicator
        if (!this.isTyping) {
            this.isTyping = true;
            this.sendTypingIndicator(true);
        }

        clearTimeout(this.typingTimer);
        this.typingTimer = setTimeout(() => {
            this.isTyping = false;
            this.sendTypingIndicator(false);
        }, 2000);

        // Auto-resize textarea
        this.autoResizeTextarea(messageInput);
    }

    handleKeyDown(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.sendMessage();
        } else if (event.key === 'Escape') {
            this.closeEmojiPicker();
            this.closeGifPicker();
        }
    }

    handlePaste(event) {
        const items = event.clipboardData.items;
        
        for (let item of items) {
            if (item.type.indexOf('image') !== -1) {
                const file = item.getAsFile();
                this.handleImagePaste(file);
                event.preventDefault();
                break;
            }
        }
    }

    sendMessage() {
        const messageInput = document.getElementById('txtMessage');
        const message = messageInput.value.trim();
        
        if (!message && this.attachedFiles.length === 0) {
            return;
        }

        // Validate message length
        if (message.length > 1000) {
            this.showNotification('Message trop long (maximum 1000 caractères)', 'error');
            return;
        }

        // Process emojis
        const processedMessage = this.processEmojis(message);

        // Send via server
        this.sendMessageToServer(processedMessage, this.attachedFiles);

        // Clear input
        messageInput.value = '';
        this.attachedFiles = [];
        this.updateAttachmentPreview();
        this.handleTyping(); // Update counter
    }

    sendMessageToServer(message, attachments) {
        // This would integrate with your ASP.NET backend
        const btnSend = document.getElementById('btnSendMessage');
        if (btnSend) {
            // Set hidden fields if needed
            const hdnMessage = document.getElementById('hdnMessage');
            if (hdnMessage) {
                hdnMessage.value = message;
            }
            
            // Trigger server-side event
            btnSend.click();
        }
    }

    sendTypingIndicator(isTyping) {
        if (!this.currentConversationId) return;

        // Send typing status to server via AJAX or SignalR
        fetch('/api/messaging/typing', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                conversationId: this.currentConversationId,
                isTyping: isTyping
            })
        }).catch(console.error);
    }

    toggleEmojiPicker() {
        const emojiModal = document.getElementById('emojiPickerModal');
        if (emojiModal) {
            const isVisible = emojiModal.style.display !== 'none';
            emojiModal.style.display = isVisible ? 'none' : 'block';
            
            if (!isVisible) {
                this.loadEmojisForCategory('recent');
            }
        }
    }

    closeEmojiPicker() {
        const emojiModal = document.getElementById('emojiPickerModal');
        if (emojiModal) {
            emojiModal.style.display = 'none';
        }
    }

    switchEmojiCategory(category) {
        // Update active category
        document.querySelectorAll('.emoji-category').forEach(cat => {
            cat.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Load emojis for category
        this.loadEmojisForCategory(category);
    }

    loadEmojisForCategory(category) {
        const emojiGrid = document.getElementById('emojiGrid');
        if (!emojiGrid) return;

        const emojis = this.getEmojisForCategory(category);
        emojiGrid.innerHTML = '';

        emojis.forEach(emoji => {
            const emojiElement = document.createElement('span');
            emojiElement.className = 'emoji-item';
            emojiElement.textContent = emoji.char;
            emojiElement.title = emoji.name;
            emojiElement.addEventListener('click', () => this.insertEmoji(emoji.char));
            emojiGrid.appendChild(emojiElement);
        });
    }

    getEmojisForCategory(category) {
        const emojiCategories = {
            recent: [
                { char: '😊', name: 'smiling face' },
                { char: '👍', name: 'thumbs up' },
                { char: '❤️', name: 'red heart' },
                { char: '😂', name: 'face with tears of joy' },
                { char: '🔥', name: 'fire' },
                { char: '🎉', name: 'party popper' }
            ],
            people: [
                { char: '😀', name: 'grinning face' },
                { char: '😃', name: 'grinning face with big eyes' },
                { char: '😄', name: 'grinning face with smiling eyes' },
                { char: '😁', name: 'beaming face with smiling eyes' },
                { char: '😆', name: 'grinning squinting face' },
                { char: '😅', name: 'grinning face with sweat' },
                { char: '😂', name: 'face with tears of joy' },
                { char: '🤣', name: 'rolling on the floor laughing' },
                { char: '😊', name: 'smiling face with smiling eyes' },
                { char: '😇', name: 'smiling face with halo' }
            ],
            nature: [
                { char: '🌱', name: 'seedling' },
                { char: '🌿', name: 'herb' },
                { char: '🍀', name: 'four leaf clover' },
                { char: '🌸', name: 'cherry blossom' },
                { char: '🌺', name: 'hibiscus' },
                { char: '🌻', name: 'sunflower' },
                { char: '🌹', name: 'rose' },
                { char: '🌷', name: 'tulip' }
            ],
            food: [
                { char: '🍎', name: 'red apple' },
                { char: '🍌', name: 'banana' },
                { char: '🍇', name: 'grapes' },
                { char: '🍓', name: 'strawberry' },
                { char: '🥝', name: 'kiwi fruit' },
                { char: '🍅', name: 'tomato' },
                { char: '🥑', name: 'avocado' },
                { char: '🌽', name: 'ear of corn' }
            ]
        };

        return emojiCategories[category] || [];
    }

    insertEmoji(emoji) {
        const messageInput = document.getElementById('txtMessage');
        if (!messageInput) return;

        const cursorPos = messageInput.selectionStart;
        const textBefore = messageInput.value.substring(0, cursorPos);
        const textAfter = messageInput.value.substring(cursorPos);

        messageInput.value = textBefore + emoji + textAfter;
        messageInput.focus();
        messageInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

        this.handleTyping();
        this.closeEmojiPicker();
    }

    processEmojis(text) {
        // Convert emoji shortcodes to actual emojis
        const emojiMap = {
            ':smile:': '😊',
            ':thumbs_up:': '👍',
            ':heart:': '❤️',
            ':laugh:': '😂',
            ':fire:': '🔥',
            ':party:': '🎉'
        };

        let processedText = text;
        for (const [shortcode, emoji] of Object.entries(emojiMap)) {
            processedText = processedText.replace(new RegExp(shortcode, 'g'), emoji);
        }

        return processedText;
    }

    openFileDialog() {
        const fileInput = document.getElementById('fileUpload');
        if (fileInput) {
            fileInput.click();
        }
    }

    setupFileUpload() {
        const fileInput = document.getElementById('fileUpload');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }
    }

    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        
        files.forEach(file => {
            if (this.validateFile(file)) {
                this.attachedFiles.push(file);
            }
        });

        this.updateAttachmentPreview();
        event.target.value = ''; // Reset input
    }

    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'text/plain',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];

        if (file.size > maxSize) {
            this.showNotification('Fichier trop volumineux (maximum 10 Mo)', 'error');
            return false;
        }

        if (!allowedTypes.includes(file.type)) {
            this.showNotification('Type de fichier non autorisé', 'error');
            return false;
        }

        return true;
    }

    updateAttachmentPreview() {
        // Update UI to show attached files
        const previewContainer = document.getElementById('attachmentPreview');
        if (!previewContainer) return;

        if (this.attachedFiles.length === 0) {
            previewContainer.style.display = 'none';
            return;
        }

        previewContainer.style.display = 'block';
        previewContainer.innerHTML = '';

        this.attachedFiles.forEach((file, index) => {
            const fileElement = this.createFilePreviewElement(file, index);
            previewContainer.appendChild(fileElement);
        });
    }

    createFilePreviewElement(file, index) {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'attachment-item';
        
        const icon = this.getFileIcon(file.type);
        const size = this.formatFileSize(file.size);
        
        fileDiv.innerHTML = `
            <span class="file-icon">${icon}</span>
            <div class="file-info">
                <span class="file-name">${file.name}</span>
                <span class="file-size">${size}</span>
            </div>
            <button type="button" class="remove-file-btn" onclick="messaging.removeAttachment(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;

        return fileDiv;
    }

    removeAttachment(index) {
        this.attachedFiles.splice(index, 1);
        this.updateAttachmentPreview();
    }

    getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return '🖼️';
        if (mimeType === 'application/pdf') return '📄';
        if (mimeType.includes('word')) return '📝';
        if (mimeType.startsWith('audio/')) return '🎵';
        if (mimeType.startsWith('video/')) return '🎥';
        return '📎';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    autoResizeTextarea(textarea) {
        if (!textarea) return;
        
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    scrollToBottom() {
        const messagesArea = document.getElementById('messagesScrollArea');
        if (messagesArea) {
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
    }

    filterConversations(filter) {
        // Update active tab
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        // Filter conversations (this would integrate with server-side filtering)
        console.log('Filtering conversations by:', filter);
    }

    handleReaction(reactionBtn) {
        const reaction = reactionBtn.dataset.reaction;
        const messageId = reactionBtn.closest('.message-group').dataset.messageId;
        
        // Send reaction to server
        this.sendReaction(messageId, reaction);
    }

    sendReaction(messageId, reaction) {
        fetch('/api/messaging/reaction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                messageId: messageId,
                reaction: reaction
            })
        }).catch(console.error);
    }

    showNotification(message, type = 'info') {
        // Create and show notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    setupAutoRefresh() {
        // Refresh messages every 30 seconds
        setInterval(() => {
            if (this.currentConversationId) {
                this.refreshMessages();
            }
        }, 30000);
    }

    refreshMessages() {
        // This would trigger a server-side refresh
        __doPostBack('refreshMessages', '');
    }

    setupTypingIndicator() {
        // Setup real-time typing indicators (would use SignalR in production)
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'k':
                        e.preventDefault();
                        document.getElementById('txtRechercheContact')?.focus();
                        break;
                    case 'Enter':
                        e.preventDefault();
                        this.sendMessage();
                        break;
                }
            }
        });
    }
}

// Initialize messaging when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.messaging = new LinkedInMessaging();
});

// Utility functions for ASP.NET integration
function selectConversation(conversationId) {
    window.messaging.currentConversationId = conversationId;
    window.messaging.scrollToBottom();
}

function addNewMessage(messageData) {
    // Add new message to UI without full page refresh
    const messagesArea = document.getElementById('messagesScrollArea');
    if (messagesArea && messageData) {
        const messageElement = createMessageElement(messageData);
        messagesArea.appendChild(messageElement);
        window.messaging.scrollToBottom();
    }
}

function createMessageElement(messageData) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-group ${messageData.isSent ? 'sent' : 'received'}`;
    messageDiv.innerHTML = `
        <div class="message-avatar">
            <img src="${messageData.avatar}" alt="Avatar" />
        </div>
        <div class="message-content">
            <div class="message-bubble">
                <div class="message-text">${messageData.content}</div>
            </div>
            <div class="message-meta">
                <span class="message-time">${messageData.time}</span>
                ${messageData.isSent ? `<span class="message-status">${messageData.status}</span>` : ''}
            </div>
        </div>
    `;
    return messageDiv;
}
