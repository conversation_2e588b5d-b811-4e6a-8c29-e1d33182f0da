using LinCom.Model;
using System;

namespace LinCom.Classe
{
    public class LinkedInParticipant_Class
    {
        public long UserId { get; set; }
        public string Name { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string PhotoUrl { get; set; }
        public string Title { get; set; }
        public string Company { get; set; }
        public string Department { get; set; }

        // Statut de présence
        public bool IsOnline { get; set; }
        public Nullable<DateTime> LastSeen { get; set; }
        public string PresenceStatus { get; set; } // "online", "away", "busy", "offline"
        public string StatusMessage { get; set; }

        // Informations de conversation
        public Nullable<DateTime> JoinedAt { get; set; }
        public Nullable<DateTime> LeftAt { get; set; }
        public bool IsActive { get; set; }
        public string Role { get; set; } // "admin", "moderator", "member"
        public bool CanAddMembers { get; set; }
        public bool CanRemoveMembers { get; set; }
        public bool CanEditConversation { get; set; }

        // Statistiques de participation
        public int MessageCount { get; set; }
        public Nullable<DateTime> LastMessageDate { get; set; }
        public int UnreadCount { get; set; }
        public bool HasUnreadMessages { get; set; }

        // Paramètres de notification
        public bool NotificationsEnabled { get; set; }
        public bool MentionNotificationsEnabled { get; set; }
        public bool SoundEnabled { get; set; }
        public string NotificationFrequency { get; set; } // "all", "mentions", "none"

        // Statut de frappe
        public bool IsTyping { get; set; }
        public Nullable<DateTime> TypingStartedAt { get; set; }

        // Informations professionnelles
        public string LinkedInProfile { get; set; }
        public string PhoneNumber { get; set; }
        public string Location { get; set; }
        public string TimeZone { get; set; }
        public string Language { get; set; }

        // Préférences
        public string Theme { get; set; }
        public bool ShowOnlineStatus { get; set; }
        public bool ShowLastSeen { get; set; }
        public bool AllowDirectMessages { get; set; }

        // Informations de blocage/restriction
        public bool IsBlocked { get; set; }
        public bool IsMuted { get; set; }
        public Nullable<DateTime> MutedUntil { get; set; }
        public string BlockReason { get; set; }

        // Navigation properties
        public virtual Membre Membre { get; set; }

        public LinkedInParticipant_Class()
        {
            IsOnline = false;
            IsActive = true;
            Role = "member";
            CanAddMembers = false;
            CanRemoveMembers = false;
            CanEditConversation = false;
            NotificationsEnabled = true;
            MentionNotificationsEnabled = true;
            SoundEnabled = true;
            NotificationFrequency = "all";
            IsTyping = false;
            Theme = "default";
            ShowOnlineStatus = true;
            ShowLastSeen = true;
            AllowDirectMessages = true;
            IsBlocked = false;
            IsMuted = false;
            MessageCount = 0;
            UnreadCount = 0;
            HasUnreadMessages = false;
            JoinedAt = DateTime.Now;
            PresenceStatus = "offline";
        }

        // Méthodes utilitaires
        public string GetFullName()
        {
            return $"{FirstName} {LastName}".Trim();
        }

        public string GetDisplayName()
        {
            return !string.IsNullOrEmpty(Name) ? Name : GetFullName();
        }

        public string GetPresenceText()
        {
            switch (PresenceStatus?.ToLower())
            {
                case "online":
                    return "En ligne";
                case "away":
                    return "Absent";
                case "busy":
                    return "Occupé";
                case "offline":
                default:
                    if (LastSeen.HasValue)
                    {
                        var timeAgo = DateTime.Now - LastSeen.Value;
                        if (timeAgo.TotalMinutes < 1)
                            return "À l'instant";
                        else if (timeAgo.TotalMinutes < 60)
                            return $"Il y a {(int)timeAgo.TotalMinutes} min";
                        else if (timeAgo.TotalHours < 24)
                            return $"Il y a {(int)timeAgo.TotalHours}h";
                        else
                            return $"Il y a {(int)timeAgo.TotalDays}j";
                    }
                    return "Hors ligne";
            }
        }

        public string GetRoleDisplayName()
        {
            switch (Role?.ToLower())
            {
                case "admin":
                    return "Administrateur";
                case "moderator":
                    return "Modérateur";
                case "member":
                default:
                    return "Membre";
            }
        }

        public bool CanPerformAction(string action)
        {
            switch (action?.ToLower())
            {
                case "add_members":
                    return CanAddMembers || Role == "admin";
                case "remove_members":
                    return CanRemoveMembers || Role == "admin";
                case "edit_conversation":
                    return CanEditConversation || Role == "admin";
                default:
                    return false;
            }
        }
    }
}
