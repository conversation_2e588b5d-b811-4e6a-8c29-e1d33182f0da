# 🏗️ Architecture LinkedIn Messaging - Pattern LinCom

## 📋 **Vue d'Ensemble**

Cette documentation explique comment les classes LinkedIn Messaging ont été restructurées pour suivre le pattern architectural utilisé dans le projet LinCom.

---

## 🎯 **Pattern Architectural LinCom**

Le projet LinCom suit un pattern en 3 couches :

### **1. Couche Classe (Business Logic)**
- **Localisation** : `/LinCom/Classe/`
- **Rôle** : Classes métier avec logique applicative
- **Exemple** : `Membre_Class.cs`, `Message_Class.cs`

### **2. Couche Imp (Data Access)**
- **Localisation** : `/LinCom/Imp/`
- **Rôle** : Interfaces et implémentations pour l'accès aux données
- **Pattern** : Repository Pattern
- **Exemple** : `IMembre.cs` + `MembreImp.cs`

### **3. Couche Présentation**
- **Localisation** : Pages `.aspx` et `.aspx.cs`
- **Rôle** : Interface utilisateur et contrôleurs

---

## 📁 **Structure LinkedIn Messaging Adaptée**

### **Classes Métier (/Classe/)**

#### **1. LinkedInConversation_Class.cs**
```csharp
public class LinkedInConversation_Class
{
    // Propriétés de base
    public long ConversationId { get; set; }
    public string Sujet { get; set; }
    public bool? IsGroup { get; set; }
    
    // Propriétés calculées pour l'affichage
    public string ParticipantName { get; set; }
    public int UnreadCount { get; set; }
    public bool HasUnread { get; set; }
    
    // Collections
    public List<LinkedInParticipant_Class> Participants { get; set; }
    
    // Navigation properties
    public virtual ICollection<Message> Messages { get; set; }
}
```

#### **2. LinkedInMessage_Class.cs**
```csharp
public class LinkedInMessage_Class
{
    // Propriétés de base
    public long MessageId { get; set; }
    public string Contenu { get; set; }
    public DateTime? DateEnvoi { get; set; }
    
    // Propriétés étendues LinkedIn
    public string MessageType { get; set; } // "text", "image", "voice"
    public string AttachmentType { get; set; }
    
    // Propriétés pour l'affichage
    public string SenderName { get; set; }
    public bool IsSentByCurrentUser { get; set; }
    
    // Réactions et statuts
    public List<LinkedInMessageReaction_Class> Reactions { get; set; }
    public List<LinkedInMessageStatus_Class> ReadByUsers { get; set; }
}
```

#### **3. LinkedInParticipant_Class.cs**
```csharp
public class LinkedInParticipant_Class
{
    // Informations utilisateur
    public long UserId { get; set; }
    public string Name { get; set; }
    public string PhotoUrl { get; set; }
    
    // Statut de présence
    public bool IsOnline { get; set; }
    public DateTime? LastSeen { get; set; }
    public string PresenceStatus { get; set; } // "online", "away", "busy"
    
    // Rôles et permissions
    public string Role { get; set; } // "admin", "moderator", "member"
    public bool CanAddMembers { get; set; }
    
    // Méthodes utilitaires
    public string GetPresenceText() { /* ... */ }
    public bool CanPerformAction(string action) { /* ... */ }
}
```

#### **4. LinkedInMessageStatus_Class.cs**
```csharp
public class LinkedInMessageStatus_Class
{
    // Statuts de lecture
    public int IsRead { get; set; }
    public DateTime? ReadAt { get; set; }
    public int IsDelivered { get; set; }
    
    // Informations de plateforme
    public string DeviceType { get; set; } // "web", "mobile"
    public string Platform { get; set; } // "android", "ios", "web"
    
    // Méthodes utilitaires
    public string GetStatusText() { /* ... */ }
    public string GetStatusIcon() { /* ... */ }
    public void MarkAsRead() { /* ... */ }
}
```

#### **5. LinkedInMessageReaction_Class.cs**
```csharp
public class LinkedInMessageReaction_Class
{
    public string ReactionType { get; set; } // "like", "love", "laugh"
    public DateTime? CreatedAt { get; set; }
    
    // Méthodes utilitaires
    public string GetReactionEmoji() { /* ... */ }
    public string GetReactionText() { /* ... */ }
    public string GetReactionColor() { /* ... */ }
}
```

#### **6. LinkedInMessagingStats_Class.cs**
```csharp
public class LinkedInMessagingStats_Class
{
    // Statistiques générales
    public int TotalConversations { get; set; }
    public int UnreadMessages { get; set; }
    public int MessagesSentToday { get; set; }
    
    // Statistiques de fichiers
    public int FilesShared { get; set; }
    public long TotalFileSize { get; set; }
    
    // Méthodes utilitaires
    public string GetFormattedFileSize() { /* ... */ }
    public string GetActivityLevel() { /* ... */ }
    public double GetEngagementRate() { /* ... */ }
}
```

### **Interface et Implémentation (/Imp/)**

#### **ILinkedInMessaging.cs**
```csharp
internal interface ILinkedInMessaging
{
    // Gestion des conversations
    void ChargerConversationsAvecDetails(ListView listView, long userId);
    int CreerNouvelleConversation(LinkedInConversation_Class conversation, long[] participantIds);
    int ModifierConversation(LinkedInConversation_Class conversation);
    
    // Gestion des messages
    void ChargerMessagesAvecDetails(Repeater rpt, long conversationId, int nombreMessages = 50);
    int EnvoyerMessageAvecStatut(LinkedInMessage_Class message);
    
    // Recherche et filtres
    void RechercherConversations(ListView listView, long userId, string searchTerm);
    void FiltrerConversations(ListView listView, long userId, string filterType);
    
    // Statuts et présence
    int MettreAJourStatutPresence(long userId, bool isOnline);
    bool VerifierStatutEnLigne(long userId);
    
    // Fonctionnalités avancées
    int EpinglerConversation(long conversationId, long userId, bool isPinned);
    int AjouterReactionMessage(long messageId, long userId, string reactionType);
    
    // Statistiques
    void ObtenirStatistiquesMessagerie(long userId, LinkedInMessagingStats_Class statsClass);
    
    // Utilitaires
    string FormatTempsEcoule(DateTime dateTime);
    string ConvertirEmojisEnTexte(string message);
}
```

#### **LinkedInMessagingImp.cs**
```csharp
public class LinkedInMessagingImp : ILinkedInMessaging
{
    public void ChargerConversationsAvecDetails(ListView listView, long userId)
    {
        try
        {
            using (var context = new Connection())
            {
                // Logique d'accès aux données
                var conversations = (from c in context.Conversations
                                   join pc in context.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                   where pc.MembreId == userId
                                   select new { /* ... */ }).ToList();
                
                listView.DataSource = conversations;
                listView.DataBind();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Erreur: {ex.Message}");
            throw;
        }
    }
    
    // Autres implémentations...
}
```

### **Gestionnaire Principal (/Classe/)**

#### **LinkedInMessagingManager_Class.cs**
```csharp
public class LinkedInMessagingManager_Class
{
    private readonly ILinkedInMessaging _linkedInMessagingService;

    public LinkedInMessagingManager_Class()
    {
        _linkedInMessagingService = new LinkedInMessagingImp();
    }

    #region Méthodes publiques principales

    public void ChargerConversationsAvecDetails(ListView listView, long userId)
    {
        _linkedInMessagingService.ChargerConversationsAvecDetails(listView, userId);
    }

    public int EnvoyerMessageAvecStatut(LinkedInMessage_Class message)
    {
        return _linkedInMessagingService.EnvoyerMessageAvecStatut(message);
    }

    #endregion

    #region Méthodes utilitaires

    public List<LinkedInConversation_Class> ObtenirConversationsAvecDetails(long userId)
    {
        // Logique métier spécifique
    }

    #endregion
}
```

---

## 🔄 **Utilisation dans les Pages**

### **Dans le Code-Behind (.aspx.cs)**
```csharp
public partial class messagerie_linkedin : System.Web.UI.Page
{
    private LinkedInMessagingManager_Class _messagingManager;

    protected void Page_Load(object sender, EventArgs e)
    {
        _messagingManager = new LinkedInMessagingManager_Class();
        
        if (!IsPostBack)
        {
            ChargerConversations();
        }
    }

    private void ChargerConversations()
    {
        long userId = GetCurrentUserId();
        _messagingManager.ChargerConversationsAvecDetails(listConversations, userId);
    }

    protected void btnSendMessage_Click(object sender, EventArgs e)
    {
        var message = new LinkedInMessage_Class
        {
            ConversationId = GetCurrentConversationId(),
            SenderId = GetCurrentUserId(),
            Contenu = txtMessage.Text,
            DateEnvoi = DateTime.Now
        };

        int messageId = _messagingManager.EnvoyerMessageAvecStatut(message);
        
        if (messageId > 0)
        {
            // Succès
            ChargerMessages();
        }
    }
}
```

---

## 🎯 **Avantages de cette Architecture**

### **1. Séparation des Responsabilités**
- **Classes métier** : Logique applicative et validation
- **Implémentations** : Accès aux données uniquement
- **Gestionnaires** : Orchestration des opérations

### **2. Testabilité**
- Interfaces permettent les mocks
- Logique métier isolée
- Tests unitaires facilités

### **3. Maintenabilité**
- Code organisé et structuré
- Responsabilités claires
- Évolution facilitée

### **4. Réutilisabilité**
- Classes métier réutilisables
- Services partagés
- Pattern cohérent

### **5. Extensibilité**
- Nouvelles fonctionnalités faciles à ajouter
- Interfaces extensibles
- Architecture modulaire

---

## 📝 **Conventions de Nommage**

### **Classes Métier**
- Format : `[Nom]_Class.cs`
- Exemple : `LinkedInConversation_Class.cs`

### **Interfaces**
- Format : `I[Nom].cs`
- Exemple : `ILinkedInMessaging.cs`

### **Implémentations**
- Format : `[Nom]Imp.cs`
- Exemple : `LinkedInMessagingImp.cs`

### **Gestionnaires**
- Format : `[Nom]Manager_Class.cs`
- Exemple : `LinkedInMessagingManager_Class.cs`

---

## 🚀 **Migration depuis l'Ancienne Structure**

### **Avant (Structure Statique)**
```csharp
public static class LinkedInMessagingManager
{
    public static List<ConversationInfo> GetUserConversations(long userId)
    {
        // Logique mélangée
    }
}
```

### **Après (Pattern LinCom)**
```csharp
// Interface
public interface ILinkedInMessaging { /* ... */ }

// Implémentation
public class LinkedInMessagingImp : ILinkedInMessaging { /* ... */ }

// Gestionnaire
public class LinkedInMessagingManager_Class
{
    private readonly ILinkedInMessaging _service;
    // Logique métier séparée
}
```

---

## 📚 **Ressources et Références**

- **Pattern Repository** : Séparation accès données / logique métier
- **Dependency Injection** : Inversion de contrôle
- **SOLID Principles** : Principes de conception orientée objet
- **Clean Architecture** : Architecture en couches

---

**🎉 Votre module LinkedIn Messaging suit maintenant parfaitement le pattern architectural LinCom !**
