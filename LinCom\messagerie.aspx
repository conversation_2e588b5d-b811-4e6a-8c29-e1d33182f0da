﻿<%@ Page Title="Messagerie LinCom" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- LinkedIn-style Messaging CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">👥 Contacts</div>
                    <div class="contacts-search">
                        <asp:TextBox ID="txtRechercheContact" runat="server" placeholder="Rechercher un contact..." AutoPostBack="true" OnTextChanged="txtRechercheContact_TextChanged" CssClass="search-input"></asp:TextBox>
                    </div>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <p><%# Server.HtmlDecode(LinCom.Classe.EmojiManager.ConvertirEmojis(Eval("Contenu").ToString())) %></p>

                <%-- Si le message contient une pièce jointe --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <div class="message-attachment">
                        <span class="attachment-icon"><%# GetFileIcon(Eval("AttachmentUrl").ToString()) %></span>
                        <div class="attachment-details">
                            <div class="attachment-name"><%# Eval("name") %></div>
                            <div class="attachment-size"><%# GetFileSize(Eval("AttachmentUrl").ToString()) %></div>
                        </div>
                        <a href='<%# Eval("AttachmentUrl") %>' class="attachment-download" target="_blank">Télécharger</a>
                    </div>
                </asp:Panel>

                <%-- Affichage du statut de lecture pour les messages envoyés --%>
                <div class="message-footer">
                    <span class="message-status status-read">✓✓</span>
                </div>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-footer">
                        <div class="message-input-container">
                            <div class="input-toolbar">
                                <button type="button" id="btnEmoji" class="toolbar-btn" title="Émojis">😊</button>
                                <button type="button" id="btnAttachment" class="toolbar-btn" title="Pièce jointe">📎</button>
                                <input type="file" id="fileAttachment" style="display:none;" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.rar,.mp3,.mp4,.avi" />
                            </div>
                            <textarea rows="2" runat="server" id="txtMessage" placeholder="Écrivez votre message..." maxlength="1000"></textarea>
                            <button type="button" runat="server" id="btnenvoie" onserverclick="btnenvoie_ServerClick" class="send-btn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- Sélecteur d'émojis -->
                        <div id="emojiPicker" class="emoji-picker" style="display:none;">
                            <div class="emoji-header">
                                <span class="emoji-tab active" data-category="populaires">😊</span>
                                <span class="emoji-tab" data-category="visages">😀</span>
                                <span class="emoji-tab" data-category="gestes">👍</span>
                                <span class="emoji-tab" data-category="objets">❤️</span>
                                <span class="emoji-tab" data-category="nature">🌳</span>
                            </div>
                            <div class="emoji-content" id="emojiContent">
                                <!-- Émojis populaires par défaut -->
                                <div class="emoji-grid">
                                    <span class="emoji-item" data-emoji="😊">😊</span>
                                    <span class="emoji-item" data-emoji="😂">😂</span>
                                    <span class="emoji-item" data-emoji="❤️">❤️</span>
                                    <span class="emoji-item" data-emoji="👍">👍</span>
                                    <span class="emoji-item" data-emoji="😢">😢</span>
                                    <span class="emoji-item" data-emoji="😉">😉</span>
                                    <span class="emoji-item" data-emoji="🔥">🔥</span>
                                    <span class="emoji-item" data-emoji="🎉">🎉</span>
                                    <span class="emoji-item" data-emoji="😍">😍</span>
                                    <span class="emoji-item" data-emoji="👏">👏</span>
                                </div>
                            </div>
                        </div>

                        <!-- Prévisualisation de la pièce jointe -->
                        <div id="attachmentPreview" class="attachment-preview" style="display:none;">
                            <div class="attachment-info">
                                <span id="attachmentName"></span>
                                <span id="attachmentSize"></span>
                                <button type="button" id="btnRemoveAttachment" class="remove-btn">×</button>
                            </div>
                        </div>
                    </div>
                    <div class="message-counter">
                        <small id="charCount">0/1000 caractères</small>
                    </div>

                    <!-- Champ caché pour la pièce jointe -->
                    <asp:HiddenField ID="hdnAttachmentPath" runat="server" />
                </div>
            </div>
        </div>

    </main>

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
        }

        .chat-wrapper {
            display: flex;
            height: 80vh;
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
            background: #fff;
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

            .chat-footer textarea {
                flex: 1;
                border-radius: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                resize: none;
            }

            .chat-footer button {
                background: #008374;
                color: #fff;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                font-weight: bold;
            }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

            .contacts-search input {
                width: 100%;
                padding: 8px;
                border-radius: 8px;
                border: 1px solid #ccc;
            }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

        .message-counter {
            padding: 5px 15px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            text-align: right;
        }

        .search-input {
            width: 100%;
            padding: 8px;
            border-radius: 8px;
            border: 1px solid #ccc;
        }

        .typing-indicator {
            font-style: italic;
            color: #666;
            padding: 10px;
            display: none;
        }

        /* Styles pour les nouvelles fonctionnalités */
        .message-input-container {
            display: flex;
            align-items: flex-end;
            gap: 10px;
            position: relative;
        }

        .input-toolbar {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .toolbar-btn {
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: #e0e0e0;
            transform: scale(1.1);
        }

        .send-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            background: #0056b3;
            transform: scale(1.05);
        }

        /* Sélecteur d'émojis */
        .emoji-picker {
            position: absolute;
            bottom: 100%;
            left: 0;
            width: 300px;
            height: 250px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        .emoji-header {
            display: flex;
            border-bottom: 1px solid #eee;
            padding: 10px;
        }

        .emoji-tab {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 5px;
            margin-right: 5px;
            transition: background 0.3s ease;
        }

        .emoji-tab:hover, .emoji-tab.active {
            background: #f0f0f0;
        }

        .emoji-content {
            padding: 10px;
            height: 180px;
            overflow-y: auto;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 5px;
        }

        .emoji-item {
            padding: 8px;
            text-align: center;
            cursor: pointer;
            border-radius: 5px;
            font-size: 18px;
            transition: background 0.3s ease;
        }

        .emoji-item:hover {
            background: #f0f0f0;
            transform: scale(1.2);
        }

        /* Prévisualisation des pièces jointes */
        .attachment-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .attachment-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        /* Statuts de messages */
        .message-status {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }

        .status-sent { color: #6c757d; }
        .status-delivered { color: #007bff; }
        .status-read { color: #28a745; }

        /* Notifications */
        .notification-badge {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            position: absolute;
            top: -5px;
            right: -5px;
            min-width: 18px;
            text-align: center;
        }

        /* Pièces jointes dans les messages */
        .message-attachment {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .attachment-icon {
            font-size: 24px;
        }

        .attachment-details {
            flex: 1;
        }

        .attachment-name {
            font-weight: bold;
            color: #333;
        }

        .attachment-size {
            font-size: 12px;
            color: #666;
        }

        .attachment-download {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            cursor: pointer;
            text-decoration: none;
            font-size: 12px;
        }

        .attachment-download:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }

        /* Images dans les messages */
        .message-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .message-image:hover {
            transform: scale(1.05);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .emoji-picker {
                width: 280px;
                height: 200px;
            }

            .emoji-grid {
                grid-template-columns: repeat(6, 1fr);
            }

            .message-image {
                max-width: 150px;
                max-height: 150px;
            }
        }
    </style>

    <script type="text/javascript">
        // Compteur de caractères
        function updateCharCount() {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            var counter = document.getElementById('charCount');
            if (textarea && counter) {
                var length = textarea.value.length;
                counter.textContent = length + '/1000 caractères';

                if (length > 900) {
                    counter.style.color = 'red';
                } else if (length > 800) {
                    counter.style.color = 'orange';
                } else {
                    counter.style.color = '#666';
                }
            }
        }

        // Auto-scroll vers le bas des messages
        function scrollToBottom() {
            var chatBody = document.querySelector('.chat-body');
            if (chatBody) {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        // Envoyer message avec Enter (Shift+Enter pour nouvelle ligne)
        function handleKeyPress(event) {
            if (event.keyCode === 13 && !event.shiftKey) {
                event.preventDefault();
                var btnEnvoie = document.getElementById('<%= btnenvoie.ClientID %>');
                if (btnEnvoie) {
                    btnEnvoie.click();
                }
            }
        }

        // Gestion des émojis
        function toggleEmojiPicker() {
            var picker = document.getElementById('emojiPicker');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        }

        function insertEmoji(emoji) {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            var cursorPos = textarea.selectionStart;
            var textBefore = textarea.value.substring(0, cursorPos);
            var textAfter = textarea.value.substring(cursorPos);

            textarea.value = textBefore + emoji + textAfter;
            textarea.focus();
            textarea.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

            updateCharCount();
            document.getElementById('emojiPicker').style.display = 'none';
        }

        // Gestion des pièces jointes
        function handleFileSelect() {
            var fileInput = document.getElementById('fileAttachment');
            var file = fileInput.files[0];

            if (file) {
                // Validation de la taille (10MB max)
                if (file.size > 10 * 1024 * 1024) {
                    alert('Le fichier est trop volumineux (maximum 10 Mo)');
                    fileInput.value = '';
                    return;
                }

                // Validation de l'extension
                var allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar', '.mp3', '.mp4', '.avi'];
                var fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

                if (allowedExtensions.indexOf(fileExtension) === -1) {
                    alert('Type de fichier non autorisé. Extensions autorisées: ' + allowedExtensions.join(', '));
                    fileInput.value = '';
                    return;
                }

                // Afficher la prévisualisation
                showAttachmentPreview(file);

                // Uploader le fichier immédiatement
                uploadFile(file);
            }
        }

        // Fonction pour uploader le fichier (version simplifiée)
        function uploadFile(file) {
            // Pour l'instant, on stocke juste les informations du fichier
            // L'upload réel se fera lors de l'envoi du message

            var nameSpan = document.getElementById('attachmentName');
            var sizeSpan = document.getElementById('attachmentSize');

            // Afficher les informations du fichier
            nameSpan.textContent = file.name;
            sizeSpan.textContent = formatFileSize(file.size);

            // Marquer qu'un fichier est prêt à être uploadé
            document.getElementById('<%= hdnAttachmentPath.ClientID %>').value = 'READY_TO_UPLOAD';

            console.log('Fichier prêt pour upload: ' + file.name);
        }

        function showAttachmentPreview(file) {
            var preview = document.getElementById('attachmentPreview');
            var nameSpan = document.getElementById('attachmentName');
            var sizeSpan = document.getElementById('attachmentSize');

            nameSpan.textContent = file.name;
            sizeSpan.textContent = formatFileSize(file.size);
            preview.style.display = 'block';
        }

        function removeAttachment() {
            document.getElementById('fileAttachment').value = '';
            document.getElementById('attachmentPreview').style.display = 'none';
            document.getElementById('<%= hdnAttachmentPath.ClientID %>').value = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Initialisation au chargement de la page
        window.onload = function() {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.addEventListener('input', updateCharCount);
                textarea.addEventListener('keypress', handleKeyPress);
            }

            // Gestion du bouton émoji
            document.getElementById('btnEmoji').addEventListener('click', toggleEmojiPicker);

            // Gestion du bouton pièce jointe
            document.getElementById('btnAttachment').addEventListener('click', function() {
                document.getElementById('fileAttachment').click();
            });

            // Gestion de la sélection de fichier
            document.getElementById('fileAttachment').addEventListener('change', handleFileSelect);

            // Gestion de la suppression de pièce jointe
            document.getElementById('btnRemoveAttachment').addEventListener('click', removeAttachment);

            // Gestion des clics sur les émojis
            document.querySelectorAll('.emoji-item').forEach(function(item) {
                item.addEventListener('click', function() {
                    insertEmoji(this.dataset.emoji);
                });
            });

            // Fermer le sélecteur d'émojis en cliquant ailleurs
            document.addEventListener('click', function(e) {
                var picker = document.getElementById('emojiPicker');
                var btnEmoji = document.getElementById('btnEmoji');

                if (!picker.contains(e.target) && e.target !== btnEmoji) {
                    picker.style.display = 'none';
                }
            });

            scrollToBottom();
            updateCharCount();
        };

        // Actualiser les messages toutes les 30 secondes
        setInterval(function() {
            if (document.getElementById('<%= lblId.ClientID %>').textContent !== '0') {
                __doPostBack('<%= Page.ClientID %>', 'RefreshMessages');
            }
        }, 30000);
    </script>

</asp:Content>
